# Generated by Django 5.0.4 on 2025-07-24 11:28

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0012_rating'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='accepted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='agent_notes',
            field=models.TextField(blank=True, help_text='Notes from delivery agent'),
        ),
        migrations.AddField(
            model_name='order',
            name='cash_amount_collected',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AddField(
            model_name='order',
            name='cash_collected_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='customer_signature',
            field=models.TextField(blank=True, help_text='Digital signature or confirmation'),
        ),
        migrations.AddField(
            model_name='order',
            name='delivered_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='delivery_photo',
            field=models.ImageField(blank=True, null=True, upload_to='delivery_photos/'),
        ),
        migrations.AddField(
            model_name='order',
            name='picked_up_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='rejected_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='rejection_reason',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('preparing', 'Preparing'), ('ready', 'Ready for Pickup'), ('assigned', 'Assigned to Delivery Agent'), ('accepted', 'Accepted by Agent'), ('en_route_to_restaurant', 'En Route to Restaurant'), ('arrived_at_restaurant', 'Arrived at Restaurant'), ('picked_up', 'Picked Up from Restaurant'), ('en_route_to_customer', 'En Route to Customer'), ('arrived_at_customer', 'Arrived at Customer'), ('delivered', 'Delivered'), ('cash_collected', 'Cash Collected'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('rejected', 'Rejected by Agent'), ('refunded', 'Refunded')], default='pending', max_length=30),
        ),
        migrations.AlterField(
            model_name='orderstatushistory',
            name='from_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('preparing', 'Preparing'), ('ready', 'Ready for Pickup'), ('assigned', 'Assigned to Delivery Agent'), ('accepted', 'Accepted by Agent'), ('en_route_to_restaurant', 'En Route to Restaurant'), ('arrived_at_restaurant', 'Arrived at Restaurant'), ('picked_up', 'Picked Up from Restaurant'), ('en_route_to_customer', 'En Route to Customer'), ('arrived_at_customer', 'Arrived at Customer'), ('delivered', 'Delivered'), ('cash_collected', 'Cash Collected'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('rejected', 'Rejected by Agent'), ('refunded', 'Refunded')], max_length=30),
        ),
        migrations.AlterField(
            model_name='orderstatushistory',
            name='to_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('preparing', 'Preparing'), ('ready', 'Ready for Pickup'), ('assigned', 'Assigned to Delivery Agent'), ('accepted', 'Accepted by Agent'), ('en_route_to_restaurant', 'En Route to Restaurant'), ('arrived_at_restaurant', 'Arrived at Restaurant'), ('picked_up', 'Picked Up from Restaurant'), ('en_route_to_customer', 'En Route to Customer'), ('arrived_at_customer', 'Arrived at Customer'), ('delivered', 'Delivered'), ('cash_collected', 'Cash Collected'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('rejected', 'Rejected by Agent'), ('refunded', 'Refunded')], max_length=30),
        ),
    ]
