# Generated by Django 5.0.4 on 2025-07-10 04:49

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('restaurant', '0012_restaurant_accepts_card_restaurant_accepts_cash_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Promotion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('promotion_type', models.CharField(choices=[('percentage', 'Percentage Discount'), ('fixed_amount', 'Fixed Amount Discount'), ('free_delivery', 'Free Delivery'), ('buy_one_get_one', 'Buy One Get One'), ('minimum_order', 'Minimum Order Discount')], max_length=20)),
                ('discount_applies_to', models.Char<PERSON>ield(choices=[('order_total', 'Order Total'), ('delivery_fee', 'Delivery Fee'), ('specific_items', 'Specific Items'), ('category', 'Menu Category')], default='order_total', max_length=20)),
                ('discount_percentage', models.DecimalField(blank=True, decimal_places=2, help_text='Percentage discount (0-100)', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('discount_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Fixed discount amount', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('min_order_amount', models.DecimalField(decimal_places=2, default=0, help_text='Minimum order amount to apply promotion', max_digits=8, validators=[django.core.validators.MinValueValidator(0)])),
                ('max_discount_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum discount amount (for percentage discounts)', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('usage_limit', models.PositiveIntegerField(blank=True, help_text='Maximum number of times this promotion can be used', null=True)),
                ('usage_limit_per_customer', models.PositiveIntegerField(default=1, help_text='Maximum uses per customer')),
                ('current_usage_count', models.PositiveIntegerField(default=0)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('valid_on_monday', models.BooleanField(default=True)),
                ('valid_on_tuesday', models.BooleanField(default=True)),
                ('valid_on_wednesday', models.BooleanField(default=True)),
                ('valid_on_thursday', models.BooleanField(default=True)),
                ('valid_on_friday', models.BooleanField(default=True)),
                ('valid_on_saturday', models.BooleanField(default=True)),
                ('valid_on_sunday', models.BooleanField(default=True)),
                ('valid_from_time', models.TimeField(blank=True, null=True)),
                ('valid_to_time', models.TimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('restaurant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='promotions', to='restaurant.restaurant')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PromotionCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True)),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('promotion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='codes', to='restaurant.promotion')),
            ],
        ),
        migrations.AddIndex(
            model_name='promotion',
            index=models.Index(fields=['restaurant', 'is_active'], name='restaurant__restaur_84bade_idx'),
        ),
        migrations.AddIndex(
            model_name='promotion',
            index=models.Index(fields=['start_date', 'end_date'], name='restaurant__start_d_d75d8a_idx'),
        ),
        migrations.AddIndex(
            model_name='promotion',
            index=models.Index(fields=['is_featured'], name='restaurant__is_feat_0de5d3_idx'),
        ),
        migrations.AddIndex(
            model_name='promotioncode',
            index=models.Index(fields=['code'], name='restaurant__code_0d746c_idx'),
        ),
        migrations.AddIndex(
            model_name='promotioncode',
            index=models.Index(fields=['is_active'], name='restaurant__is_acti_c669ce_idx'),
        ),
    ]
