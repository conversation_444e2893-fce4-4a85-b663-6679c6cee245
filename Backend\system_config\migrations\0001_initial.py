# Generated by Django 5.0.4 on 2025-07-10 05:11

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ChoiceOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('option_type', models.CharField(choices=[('user_role', 'User Role'), ('order_status', 'Order Status'), ('payment_method', 'Payment Method'), ('payment_status', 'Payment Status'), ('dietary_restriction', 'Dietary Restriction'), ('delivery_time_slot', 'Delivery Time Slot'), ('vehicle_type', 'Vehicle Type'), ('bank_name', 'Bank Name'), ('language', 'Language'), ('currency', 'Currency'), ('timezone', 'Timezone'), ('country', 'Country'), ('city', 'City'), ('feature_tag', 'Feature Tag'), ('sort_option', 'Sort Option')], max_length=30)),
                ('value', models.CharField(max_length=100)),
                ('label', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, help_text='Icon class or emoji', max_length=50)),
                ('color', models.CharField(blank=True, help_text='Hex color code', max_length=7)),
                ('display_order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['option_type', 'display_order', 'label'],
                'indexes': [models.Index(fields=['option_type', 'is_active'], name='system_conf_option__bb473c_idx'), models.Index(fields=['is_default'], name='system_conf_is_defa_4a7c5e_idx')],
                'unique_together': {('option_type', 'value')},
            },
        ),
        migrations.CreateModel(
            name='FilterConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('key', models.CharField(max_length=50, unique=True)),
                ('filter_type', models.CharField(choices=[('range', 'Range'), ('select', 'Select'), ('multiselect', 'Multi-Select'), ('boolean', 'Boolean'), ('search', 'Search')], max_length=20)),
                ('label', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, max_length=50)),
                ('min_value', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('max_value', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('step', models.DecimalField(decimal_places=2, default=1, max_digits=5)),
                ('options', models.JSONField(blank=True, default=list, help_text='List of options for select filters')),
                ('display_order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['display_order', 'name'],
                'indexes': [models.Index(fields=['key'], name='system_conf_key_e3c678_idx'), models.Index(fields=['is_active'], name='system_conf_is_acti_4dee5e_idx'), models.Index(fields=['is_featured'], name='system_conf_is_feat_5171e3_idx')],
            },
        ),
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('value', models.TextField()),
                ('default_value', models.TextField()),
                ('setting_type', models.CharField(choices=[('string', 'String'), ('integer', 'Integer'), ('decimal', 'Decimal'), ('boolean', 'Boolean'), ('json', 'JSON'), ('text', 'Text'), ('email', 'Email'), ('url', 'URL')], default='string', max_length=20)),
                ('category', models.CharField(choices=[('general', 'General'), ('delivery', 'Delivery'), ('payment', 'Payment'), ('notification', 'Notification'), ('security', 'Security'), ('appearance', 'Appearance'), ('business', 'Business')], default='general', max_length=20)),
                ('is_public', models.BooleanField(default=False, help_text='Can be accessed by frontend')),
                ('is_editable', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['category', 'name'],
                'indexes': [models.Index(fields=['key'], name='system_conf_key_a9cf14_idx'), models.Index(fields=['category'], name='system_conf_categor_303a19_idx'), models.Index(fields=['is_public'], name='system_conf_is_publ_762247_idx')],
            },
        ),
    ]
