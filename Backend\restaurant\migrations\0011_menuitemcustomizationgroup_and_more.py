# Generated by Django 5.0.4 on 2025-07-10 04:16

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('restaurant', '0010_restaurantreview_reviewresponse_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MenuItemCustomizationGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('is_required', models.BooleanField(default=False)),
                ('min_selections', models.PositiveIntegerField(default=0)),
                ('max_selections', models.PositiveIntegerField(default=1)),
                ('display_order', models.PositiveIntegerField(default=0)),
                ('menu_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customization_groups', to='restaurant.menuitem')),
            ],
            options={
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='MenuItemCustomizationOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('price_adjustment', models.DecimalField(decimal_places=2, default=0, help_text='Price adjustment for this option', max_digits=8)),
                ('is_default', models.BooleanField(default=False)),
                ('is_available', models.BooleanField(default=True)),
                ('display_order', models.PositiveIntegerField(default=0)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='options', to='restaurant.menuitemcustomizationgroup')),
            ],
            options={
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='MenuItemVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('price_adjustment', models.DecimalField(decimal_places=2, default=0, help_text='Price adjustment from base menu item price (can be negative)', max_digits=8)),
                ('is_default', models.BooleanField(default=False)),
                ('is_available', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('menu_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='restaurant.menuitem')),
            ],
        ),
        migrations.CreateModel(
            name='MenuItemAddon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('price', models.DecimalField(decimal_places=2, default=0, max_digits=8, validators=[django.core.validators.MinValueValidator(0)])),
                ('addon_type', models.CharField(choices=[('required', 'Required'), ('optional', 'Optional'), ('extra', 'Extra')], default='optional', max_length=20)),
                ('is_available', models.BooleanField(default=True)),
                ('max_quantity', models.PositiveIntegerField(default=1, help_text='Maximum quantity allowed')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('menu_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='addons', to='restaurant.menuitem')),
            ],
            options={
                'indexes': [models.Index(fields=['menu_item', 'addon_type'], name='restaurant__menu_it_801a58_idx'), models.Index(fields=['is_available'], name='restaurant__is_avai_5548ea_idx')],
                'unique_together': {('menu_item', 'name')},
            },
        ),
        migrations.AddIndex(
            model_name='menuitemcustomizationgroup',
            index=models.Index(fields=['menu_item', 'display_order'], name='restaurant__menu_it_1e0e72_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='menuitemcustomizationgroup',
            unique_together={('menu_item', 'name')},
        ),
        migrations.AddIndex(
            model_name='menuitemcustomizationoption',
            index=models.Index(fields=['group', 'display_order'], name='restaurant__group_i_2f6840_idx'),
        ),
        migrations.AddIndex(
            model_name='menuitemcustomizationoption',
            index=models.Index(fields=['is_available'], name='restaurant__is_avai_f22a4b_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='menuitemcustomizationoption',
            unique_together={('group', 'name')},
        ),
        migrations.AddIndex(
            model_name='menuitemvariant',
            index=models.Index(fields=['menu_item', 'is_available'], name='restaurant__menu_it_5680b2_idx'),
        ),
        migrations.AddIndex(
            model_name='menuitemvariant',
            index=models.Index(fields=['is_default'], name='restaurant__is_defa_b0a266_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='menuitemvariant',
            unique_together={('menu_item', 'name')},
        ),
    ]
