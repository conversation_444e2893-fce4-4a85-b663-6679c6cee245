import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";

const TestLogin = () => {
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleTestLogin = async () => {
    setLoading(true);
    try {
      // Try to login with test delivery agent credentials
      const result = await login("test_delivery_agent", "testpass123");

      if (result.success) {
        console.log("✅ Test login successful");
        navigate("/delivery/dashboard");
      } else {
        console.error("❌ Test login failed:", result.error);
        alert("Test login failed. Creating test user...");

        // If login fails, try to create a test user
        await createTestUser();
      }
    } catch (error) {
      console.error("❌ Login error:", error);
      alert("Login error: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  const createTestUser = async () => {
    try {
      // First create the user
      const response = await fetch("http://127.0.0.1:8000/api/auth/register/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_name: "test_delivery_agent",
          email: "<EMAIL>",
          password: "testpass123",
          name: "Test Delivery Agent",
          role: "delivery_agent",
        }),
      });

      if (response.ok) {
        console.log("✅ Test user created successfully");

        // Try to login to get the token
        const loginResult = await login("test_delivery_agent", "testpass123");
        if (loginResult.success) {
          console.log(
            "✅ Login successful, creating delivery agent profile..."
          );

          // Create delivery agent profile
          const user = JSON.parse(
            localStorage.getItem("afghanSofraUser") || "{}"
          );
          const token =
            user.access_token || localStorage.getItem("access_token");
          const profileResponse = await fetch(
            "http://127.0.0.1:8000/api/delivery-agent/register/",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify({
                phone_number: "+**********",
                vehicle_type: "motorcycle",
                license_number: "TEST123",
                vehicle_model: "Test Model",
                vehicle_year: 2023,
                emergency_contact_name: "Emergency Contact",
                emergency_contact_phone: "+**********",
              }),
            }
          );

          if (profileResponse.ok) {
            console.log("✅ Delivery agent profile created successfully");
            alert(
              "Test delivery agent created successfully! Note: Your account requires admin approval before you can start accepting orders. Redirecting to dashboard..."
            );
            navigate("/delivery/dashboard");
          } else {
            console.log(
              "⚠️ Profile creation failed, but user exists. Redirecting anyway..."
            );
            navigate("/delivery/dashboard");
          }
        }
      } else {
        const error = await response.json();
        console.error("❌ Failed to create test user:", error);
        alert("Failed to create test user: " + JSON.stringify(error));
      }
    } catch (error) {
      console.error("❌ Error creating test user:", error);
      alert("Error creating test user: " + error.message);
    }
  };

  return (
    <div className='min-h-screen bg-gray-50 flex items-center justify-center p-4'>
      <Card className='w-full max-w-md p-8'>
        <div className='text-center mb-6'>
          <h1 className='text-2xl font-bold text-gray-900 mb-2'>
            🚚 Delivery Agent Test
          </h1>
          <p className='text-gray-600'>
            Quick login for testing delivery agent features
          </p>
        </div>

        <div className='space-y-4'>
          <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
            <h3 className='font-medium text-blue-900 mb-2'>Test Credentials</h3>
            <p className='text-sm text-blue-700'>
              <strong>Username:</strong> test_delivery_agent
              <br />
              <strong>Password:</strong> testpass123
              <br />
              <strong>Role:</strong> delivery_agent
            </p>
          </div>

          <Button
            onClick={handleTestLogin}
            disabled={loading}
            className='w-full flex items-center justify-center space-x-2'
          >
            {loading ? (
              <>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                <span>Logging in...</span>
              </>
            ) : (
              <>
                <span>🚀 Login as Test Delivery Agent</span>
              </>
            )}
          </Button>

          <div className='text-center'>
            <p className='text-sm text-gray-500'>
              This will create a test delivery agent account if it doesn't exist
            </p>
          </div>

          <div className='border-t pt-4'>
            <h4 className='font-medium text-gray-900 mb-2'>
              What you can test:
            </h4>
            <ul className='text-sm text-gray-600 space-y-1'>
              <li>• 📊 Real-time dashboard with metrics</li>
              <li>• 📦 Order management and tracking</li>
              <li>• 💰 Earnings and payout system</li>
              <li>• 📱 Mobile-optimized interface</li>
              <li>• 🔄 Real-time notifications</li>
              <li>• 📍 GPS location tracking</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TestLogin;
