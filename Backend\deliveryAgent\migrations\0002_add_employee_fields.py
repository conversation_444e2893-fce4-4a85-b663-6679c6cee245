# Generated manually to add employee management fields

from django.db import migrations, models
import django.db.models.deletion
from decimal import Decimal
from django.utils import timezone


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
        ('deliveryAgent', '0001_initial'),
    ]

    operations = [
        # Add employee_number field
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='employee_number',
            field=models.CharField(max_length=10, unique=True, default='001', help_text="Employee number (e.g., 001, 002)"),
            preserve_default=False,
        ),
        
        # Add employment status
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='employment_status',
            field=models.CharField(
                max_length=20,
                choices=[
                    ('active', 'Active'),
                    ('inactive', 'Inactive'),
                    ('terminated', 'Terminated'),
                    ('suspended', 'Suspended'),
                ],
                default='active'
            ),
        ),
        
        # Add hire_date
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='hire_date',
            field=models.DateField(default=timezone.now().date(), help_text="Date of employment"),
            preserve_default=False,
        ),
        
        # Add termination_date
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='termination_date',
            field=models.DateField(null=True, blank=True),
        ),
        
        # Add salary fields
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='salary_type',
            field=models.CharField(
                max_length=20,
                choices=[
                    ('monthly', 'Monthly Salary'),
                    ('hourly', 'Hourly Rate'),
                    ('commission', 'Commission Only'),
                    ('hybrid', 'Salary + Commission'),
                ],
                default='monthly'
            ),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='base_salary',
            field=models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('15000.00'), help_text="Base salary in AFN"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='commission_per_delivery',
            field=models.DecimalField(max_digits=8, decimal_places=2, default=Decimal('100.00'), help_text="Commission per delivery in AFN"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='hourly_rate',
            field=models.DecimalField(max_digits=8, decimal_places=2, default=Decimal('0.00'), help_text="Hourly rate in AFN"),
        ),
        
        # Add work schedule fields
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='work_schedule',
            field=models.CharField(
                max_length=20,
                choices=[
                    ('full_time', 'Full Time'),
                    ('part_time', 'Part Time'),
                    ('flexible', 'Flexible'),
                    ('shift_based', 'Shift Based'),
                ],
                default='full_time'
            ),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='shift_start_time',
            field=models.TimeField(default='08:00', help_text="Shift start time"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='shift_end_time',
            field=models.TimeField(default='17:00', help_text="Shift end time"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='working_days',
            field=models.CharField(max_length=100, default='monday,tuesday,wednesday,thursday,friday,saturday', help_text="Working days (comma-separated)"),
        ),
        
        # Add clock in/out fields
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='is_clocked_in',
            field=models.BooleanField(default=False, help_text="Currently clocked in"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='last_clock_in',
            field=models.DateTimeField(null=True, blank=True, help_text="Last clock in time"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='last_clock_out',
            field=models.DateTimeField(null=True, blank=True, help_text="Last clock out time"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='current_shift_hours',
            field=models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'), help_text="Current shift hours"),
        ),
        
        # Add supervisor field
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='supervisor',
            field=models.ForeignKey(
                null=True, blank=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='users.user',
                related_name='supervised_agents',
                help_text="Supervising admin"
            ),
        ),
        
        # Add employee status fields
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='training_completed',
            field=models.BooleanField(default=False, help_text="Training completed"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='training_completion_date',
            field=models.DateField(null=True, blank=True, help_text="Training completion date"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='documents_complete',
            field=models.BooleanField(default=False, help_text="All documents submitted"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='background_check_completed',
            field=models.BooleanField(default=False, help_text="Background check completed"),
        ),
        
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='medical_clearance',
            field=models.BooleanField(default=False, help_text="Medical clearance obtained"),
        ),
        
        # Add performance notes
        migrations.AddField(
            model_name='deliveryagentprofile',
            name='performance_notes',
            field=models.TextField(blank=True, help_text="Performance notes and evaluations"),
        ),
    ]
