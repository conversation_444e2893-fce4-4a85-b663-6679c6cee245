# Generated by Django 5.0.4 on 2025-07-20 12:55

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('financial_management', '0001_initial'),
        ('orders', '0012_rating'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DeliveryAgentCommission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('base_delivery_fee', models.DecimalField(decimal_places=2, default=5.0, help_text='Base fee per delivery', max_digits=6)),
                ('distance_rate_per_km', models.DecimalField(decimal_places=2, default=0.5, help_text='Additional fee per kilometer', max_digits=5)),
                ('time_bonus_per_minute', models.DecimalField(decimal_places=2, default=0.1, help_text='Bonus per minute for fast delivery', max_digits=5)),
                ('platform_commission_rate', models.DecimalField(decimal_places=2, default=30.0, help_text="Platform's share of delivery fee (0-50%)", max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(50)])),
                ('minimum_payout_amount', models.DecimalField(decimal_places=2, default=25.0, help_text='Minimum amount for payout', max_digits=8)),
                ('payout_frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('bi_weekly', 'Bi-Weekly'), ('monthly', 'Monthly')], default='weekly', max_length=20)),
                ('performance_bonus_threshold', models.PositiveIntegerField(default=20, help_text='Minimum deliveries per week for bonus')),
                ('performance_bonus_amount', models.DecimalField(decimal_places=2, default=10.0, help_text='Weekly performance bonus amount', max_digits=6)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('delivery_agent', models.OneToOneField(limit_choices_to={'role': 'delivery_agent'}, on_delete=django.db.models.deletion.CASCADE, related_name='delivery_commission', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='DeliveryAgentEarnings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('delivery_distance_km', models.DecimalField(decimal_places=2, default=0, help_text='Delivery distance in kilometers', max_digits=6)),
                ('delivery_time_minutes', models.PositiveIntegerField(default=0, help_text='Actual delivery time in minutes')),
                ('base_fee', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('distance_bonus', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('time_bonus', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('tips', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('gross_earnings', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('platform_commission', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('net_earnings', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('is_paid', models.BooleanField(default=False)),
                ('paid_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('delivery_agent', models.ForeignKey(limit_choices_to={'role': 'delivery_agent'}, on_delete=django.db.models.deletion.CASCADE, related_name='delivery_earnings', to=settings.AUTH_USER_MODEL)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_earnings', to='orders.order')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeliveryAgentPayout',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payout_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('payout_period_start', models.DateTimeField()),
                ('payout_period_end', models.DateTimeField()),
                ('deliveries_count', models.PositiveIntegerField()),
                ('total_gross_earnings', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total_commission', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total_tips', models.DecimalField(decimal_places=2, max_digits=12)),
                ('performance_bonus', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('payment_method', models.CharField(choices=[('bank_transfer', 'Bank Transfer'), ('paypal', 'PayPal'), ('stripe', 'Stripe'), ('digital_wallet', 'Digital Wallet'), ('cash', 'Cash')], default='bank_transfer', max_length=50)),
                ('transaction_id', models.CharField(blank=True, max_length=100)),
                ('payment_reference', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('delivery_agent', models.ForeignKey(limit_choices_to={'role': 'delivery_agent'}, on_delete=django.db.models.deletion.CASCADE, related_name='delivery_payouts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeliveryAgentFinancialSummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period_type', models.CharField(choices=[('weekly', 'Weekly'), ('monthly', 'Monthly')], max_length=20)),
                ('period_start', models.DateTimeField()),
                ('period_end', models.DateTimeField()),
                ('total_deliveries', models.PositiveIntegerField(default=0)),
                ('total_distance_km', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_tips', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_bonuses', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('platform_commission', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('net_earnings', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('average_delivery_time', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('average_earnings_per_delivery', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('delivery_agent', models.ForeignKey(limit_choices_to={'role': 'delivery_agent'}, on_delete=django.db.models.deletion.CASCADE, related_name='financial_summaries', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-period_start'],
                'unique_together': {('delivery_agent', 'period_type', 'period_start')},
            },
        ),
    ]
