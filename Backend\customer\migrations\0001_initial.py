# Generated by Django 5.0.4 on 2025-07-19 05:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('restaurant', '0014_alter_restaurant_delivery_fee'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerFavorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(limit_choices_to={'role': 'customer'}, on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to=settings.AUTH_USER_MODEL)),
                ('restaurant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='restaurant.restaurant')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['customer', '-created_at'], name='customer_cu_custome_7e5df3_idx'), models.Index(fields=['restaurant', '-created_at'], name='customer_cu_restaur_92f6a0_idx')],
                'unique_together': {('customer', 'restaurant')},
            },
        ),
    ]
