# Generated by Django 5.0.4 on 2025-07-10 04:27

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('restaurant', '0011_menuitemcustomizationgroup_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='restaurant',
            name='accepts_card',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='accepts_cash',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='accepts_online_payment',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='average_preparation_time',
            field=models.PositiveIntegerField(default=30, help_text='Average preparation time in minutes'),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='facebook_url',
            field=models.URLField(blank=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='restaurant',
            name='friday_closing',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='friday_opening',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='instagram_url',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='min_order_amount',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Minimum order amount for delivery', max_digits=8, validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='monday_closing',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='monday_opening',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='saturday_closing',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='saturday_opening',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='sunday_closing',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='sunday_opening',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='thursday_closing',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='thursday_opening',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='tuesday_closing',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='tuesday_opening',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='twitter_url',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='website',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='wednesday_closing',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='restaurant',
            name='wednesday_opening',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='CuisineType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, help_text='Icon class or emoji', max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
                'indexes': [models.Index(fields=['is_active'], name='restaurant__is_acti_61c067_idx')],
            },
        ),
        migrations.AddField(
            model_name='restaurant',
            name='cuisine_types',
            field=models.ManyToManyField(blank=True, related_name='restaurants', to='restaurant.cuisinetype'),
        ),
        migrations.CreateModel(
            name='RestaurantCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, help_text='Icon class or emoji', max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Restaurant Categories',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['is_active'], name='restaurant__is_acti_ab390a_idx')],
            },
        ),
        migrations.AddField(
            model_name='restaurant',
            name='restaurant_category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='restaurants', to='restaurant.restaurantcategory'),
        ),
        migrations.CreateModel(
            name='ServiceArea',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('area_name', models.CharField(max_length=200)),
                ('postal_codes', models.TextField(help_text='Comma-separated postal codes')),
                ('delivery_fee', models.DecimalField(decimal_places=2, help_text='Delivery fee for this area', max_digits=8, validators=[django.core.validators.MinValueValidator(0)])),
                ('min_order_amount', models.DecimalField(decimal_places=2, default=0, help_text='Minimum order amount for this area', max_digits=8, validators=[django.core.validators.MinValueValidator(0)])),
                ('estimated_delivery_time', models.PositiveIntegerField(help_text='Estimated delivery time in minutes')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('restaurant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_areas', to='restaurant.restaurant')),
            ],
            options={
                'indexes': [models.Index(fields=['restaurant', 'is_active'], name='restaurant__restaur_ab9ebb_idx')],
                'unique_together': {('restaurant', 'area_name')},
            },
        ),
    ]
