# Generated by Django 5.0.4 on 2025-07-24 03:59

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deliveryAgent', '0002_add_employee_fields'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='deliveryagentprofile',
            options={'ordering': ['employee_number'], 'verbose_name': 'Delivery Agent Employee', 'verbose_name_plural': 'Delivery Agent Employees'},
        ),
        migrations.RemoveField(
            model_name='deliveryagentprofile',
            name='application_date',
        ),
        migrations.RemoveField(
            model_name='deliveryagentprofile',
            name='background_check_status',
        ),
        migrations.RemoveField(
            model_name='deliveryagentprofile',
            name='document_verification_status',
        ),
        migrations.RemoveField(
            model_name='deliveryagentprofile',
            name='training_status',
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='admin_notes',
            field=models.TextField(blank=True, help_text='Administrative notes about employee'),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='agent_id',
            field=models.CharField(help_text='Employee ID (e.g., DA001, DA002)', max_length=20, unique=True),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='approval_date',
            field=models.DateTimeField(blank=True, help_text='Date when application was approved', null=True),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='approved_by',
            field=models.ForeignKey(blank=True, help_text='Admin who approved the application', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_agents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='availability',
            field=models.CharField(choices=[('available', 'Available for Orders'), ('busy', 'On Delivery'), ('break', 'On Break'), ('offline', 'Off Duty')], default='offline', max_length=20),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='commission_per_delivery',
            field=models.DecimalField(decimal_places=2, default=Decimal('100.00'), help_text='Commission per delivery in AFN', max_digits=6),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='current_shift_hours',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=4),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='documents_complete',
            field=models.BooleanField(default=False, help_text='All required documents submitted'),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='employee_number',
            field=models.CharField(help_text='Sequential employee number', max_length=10, unique=True),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='employment_status',
            field=models.CharField(choices=[('active', 'Active Employee'), ('inactive', 'Inactive'), ('terminated', 'Terminated'), ('suspended', 'Suspended')], default='active', max_length=20),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='hourly_rate',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Hourly rate in AFN', max_digits=6),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='is_verified',
            field=models.BooleanField(default=True, help_text='Employee verification status'),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='last_clock_in',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='last_clock_out',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='performance_notes',
            field=models.TextField(blank=True, help_text='Performance evaluation notes'),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='rejected_by',
            field=models.ForeignKey(blank=True, help_text='Admin who rejected the application', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rejected_agents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='rejection_date',
            field=models.DateTimeField(blank=True, help_text='Date when application was rejected', null=True),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='status',
            field=models.CharField(choices=[('pending', 'انتظار کې (Pending)'), ('approved', 'تصویب شوی (Approved)'), ('active', 'فعال (Active)'), ('inactive', 'غیر فعال (Inactive)'), ('suspended', 'تعلیق شوی (Suspended)'), ('rejected', 'رد شوی (Rejected)')], default='pending', help_text='Application status - controlled by delivery agent', max_length=20),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='supervisor',
            field=models.ForeignKey(blank=True, limit_choices_to={'role': 'admin'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_agents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='training_completed',
            field=models.BooleanField(default=False, help_text='Training completion status'),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='training_completion_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='work_schedule',
            field=models.CharField(choices=[('full_time', 'Full Time (8 hours)'), ('part_time', 'Part Time (4-6 hours)'), ('flexible', 'Flexible Hours')], default='full_time', max_length=20),
        ),
        migrations.AlterField(
            model_name='deliveryagentprofile',
            name='working_days',
            field=models.CharField(default='monday_to_saturday', help_text='Working days pattern', max_length=20),
        ),
    ]
