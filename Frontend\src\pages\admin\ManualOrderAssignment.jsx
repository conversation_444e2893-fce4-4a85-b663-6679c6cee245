import React, { useState, useEffect } from "react";
import {
  Package,
  Users,
  Settings,
  Filter,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Star,
  Activity,
  User,
  Phone,
  MapPin,
  Clock,
  Truck,
  Search,
  Navigation,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { adminApi } from "../../services/adminApi";

const ManualOrderAssignment = () => {
  // Data states
  const [readyOrders, setReadyOrders] = useState([]);
  const [availableAgents, setAvailableAgents] = useState([]);
  const [activeDeliveries, setActiveDeliveries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [assignmentLoading, setAssignmentLoading] = useState(false);

  // Selection states
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [assignmentNotes, setAssignmentNotes] = useState("");
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);

  // Search and filter states
  const [orderSearchTerm, setOrderSearchTerm] = useState("");
  const [agentSearchTerm, setAgentSearchTerm] = useState("");
  const [orderFilter, setOrderFilter] = useState("all"); // all, high-value, urgent
  const [agentFilter, setAgentFilter] = useState("all"); // all, top-rated, nearby
  const [showFilters, setShowFilters] = useState(false);

  // Assignment view states
  const [selectedOrdersForAssignment, setSelectedOrdersForAssignment] =
    useState([]);
  const [agentViewMode, setAgentViewMode] = useState("available"); // available, offline, all

  // Success/Error states
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    loadAssignmentData();
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(loadAssignmentData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadAssignmentData = async () => {
    try {
      setLoading(true);
      setErrorMessage("");

      // Use the existing working API functions
      const [ordersResponse, agentsResponse, deliveriesResponse] =
        await Promise.all([
          adminApi.getOrderAssignmentData(), // This function exists and works
          adminApi.getActiveDeliveries(),
          Promise.resolve({ success: true, data: [] }), // Placeholder for now
        ]);

      if (ordersResponse.success) {
        // Extract data from the combined response
        setReadyOrders(ordersResponse.data?.ready_orders || []);
        setAvailableAgents(ordersResponse.data?.available_agents || []);
      } else {
        setErrorMessage("Failed to load assignment data");
      }

      if (deliveriesResponse.success) {
        setActiveDeliveries(deliveriesResponse.data || []);
      }
    } catch (error) {
      console.error("Failed to load assignment data:", error);
      setErrorMessage("Failed to load data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleQuickAssign = async (orderId, agentId) => {
    try {
      setAssignmentLoading(true);
      setErrorMessage("");

      const response = await adminApi.assignOrderToAgent({
        order_id: orderId,
        agent_id: agentId,
        notes: "",
      });

      if (response.success) {
        setSuccessMessage(
          `Order #${orderId} successfully assigned to agent ${agentId}`
        );
        loadAssignmentData(); // Refresh data

        // Clear success message after 5 seconds
        setTimeout(() => setSuccessMessage(""), 5000);
      } else {
        setErrorMessage(response.error?.message || "Assignment failed");
      }
    } catch (error) {
      console.error("Assignment failed:", error);
      setErrorMessage("Assignment failed. Please try again.");
    } finally {
      setAssignmentLoading(false);
    }
  };

  const toggleOrderSelection = (orderId) => {
    setSelectedOrdersForAssignment((prev) =>
      prev.includes(orderId)
        ? prev.filter((id) => id !== orderId)
        : [...prev, orderId]
    );
  };

  const selectAllOrders = () => {
    setSelectedOrdersForAssignment(filteredOrders.map((order) => order.id));
  };

  const clearOrderSelection = () => {
    setSelectedOrdersForAssignment([]);
  };

  // Filter orders based on search and filter criteria
  const filteredOrders = readyOrders.filter((order) => {
    const matchesSearch =
      !orderSearchTerm ||
      order.customer_name
        ?.toLowerCase()
        .includes(orderSearchTerm.toLowerCase()) ||
      order.restaurant_name
        ?.toLowerCase()
        .includes(orderSearchTerm.toLowerCase()) ||
      order.id.toString().includes(orderSearchTerm);

    const matchesFilter = (() => {
      switch (orderFilter) {
        case "high-value":
          return parseFloat(order.total_amount) >= 500;
        case "urgent":
          const orderTime = new Date(order.created_at);
          const now = new Date();
          const diffMinutes = (now - orderTime) / (1000 * 60);
          return diffMinutes > 30;
        default:
          return true;
      }
    })();

    return matchesSearch && matchesFilter;
  });

  // Filter agents based on search and filter criteria
  const filteredAgents = availableAgents.filter((agent) => {
    const matchesSearch =
      !agentSearchTerm ||
      agent.full_name?.toLowerCase().includes(agentSearchTerm.toLowerCase()) ||
      agent.agent_id?.toLowerCase().includes(agentSearchTerm.toLowerCase()) ||
      agent.phone_number?.includes(agentSearchTerm);

    const matchesFilter = (() => {
      switch (agentFilter) {
        case "top-rated":
          return parseFloat(agent.rating || 0) >= 4.0;
        case "nearby":
          return agent.current_location?.address;
        default:
          return true;
      }
    })();

    return matchesSearch && matchesFilter;
  });

  // Helper functions
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getOrderPriority = (order) => {
    const orderTime = new Date(order.created_at);
    const now = new Date();
    const diffMinutes = (now - orderTime) / (1000 * 60);
    const amount = parseFloat(order.total_amount);

    if (diffMinutes > 45) {
      return { level: "urgent", color: "red" };
    } else if (diffMinutes > 30) {
      return { level: "high", color: "orange" };
    } else if (amount >= 500) {
      return { level: "high-value", color: "purple" };
    }
    return { level: "normal", color: "gray" };
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      assigned: { variant: "info", text: "Assigned" },
      picked_up: { variant: "warning", text: "Picked Up" },
      in_transit: { variant: "warning", text: "In Transit" },
      delivered: { variant: "success", text: "Delivered" },
      cancelled: { variant: "danger", text: "Cancelled" },
    };

    const config = statusConfig[status] || {
      variant: "secondary",
      text: status,
    };
    return <Badge variant={config.variant}>{config.text}</Badge>;
  };

  return (
    <div className='min-h-screen bg-gradient-to-br from-gray-50 to-gray-100'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8'>
        {/* Header */}
        <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 lg:mb-8 space-y-4 lg:space-y-0'>
          <div className='flex-1'>
            <h1 className='text-2xl sm:text-3xl font-bold text-gray-900 flex items-center'>
              <Activity className='mr-2 sm:mr-3 flex-shrink-0' size={28} />
              <span className='truncate'>Order Assignment Management</span>
            </h1>
            <p className='text-gray-600 mt-2 text-sm sm:text-base'>
              Efficiently assign delivery orders to available agents
            </p>
          </div>
          <div className='flex items-center space-x-2 sm:space-x-3 flex-shrink-0'>
            <Button
              variant='outline'
              onClick={() => setShowFilters(!showFilters)}
              icon={<Filter size={16} />}
              className='text-sm'
            >
              <span className='hidden sm:inline'>Filters</span>
              <span className='sm:hidden'>Filter</span>
            </Button>
            <Button
              variant='outline'
              onClick={loadAssignmentData}
              loading={loading}
              icon={<RefreshCw size={16} />}
              className='text-sm'
            >
              <span className='hidden sm:inline'>Refresh</span>
              <span className='sm:hidden'>Sync</span>
            </Button>
          </div>
        </div>

        {/* Success/Error Messages */}
        {successMessage && (
          <div className='mb-6 p-4 bg-green-50 border border-green-200 rounded-lg'>
            <div className='flex items-center'>
              <CheckCircle className='h-5 w-5 text-green-600 mr-2' />
              <p className='text-green-800'>{successMessage}</p>
            </div>
          </div>
        )}

        {errorMessage && (
          <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-lg'>
            <div className='flex items-center'>
              <AlertCircle className='h-5 w-5 text-red-600 mr-2' />
              <p className='text-red-800'>{errorMessage}</p>
            </div>
          </div>
        )}

        {/* Summary Cards */}
        <div className='grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 lg:mb-8'>
          <Card className='p-4 sm:p-6 shadow-lg hover:shadow-xl transition-shadow duration-200 border-l-4 border-blue-500'>
            <div className='flex items-center'>
              <div className='p-2 sm:p-3 bg-blue-100 rounded-lg flex-shrink-0'>
                <Package className='h-5 w-5 sm:h-6 sm:w-6 text-blue-600' />
              </div>
              <div className='ml-3 sm:ml-4 min-w-0 flex-1'>
                <p className='text-xs sm:text-sm font-medium text-gray-600 truncate'>
                  Ready Orders
                </p>
                <p className='text-xl sm:text-2xl font-bold text-gray-900'>
                  {filteredOrders.length}
                </p>
              </div>
            </div>
          </Card>

          <Card className='p-6 shadow-lg'>
            <div className='flex items-center'>
              <div className='p-3 bg-green-100 rounded-lg'>
                <Users className='h-6 w-6 text-green-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>
                  Available Agents
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  {
                    availableAgents.filter(
                      (agent) => agent.availability === "online"
                    ).length
                  }
                </p>
              </div>
            </div>
          </Card>

          <Card className='p-6 shadow-lg'>
            <div className='flex items-center'>
              <div className='p-3 bg-orange-100 rounded-lg'>
                <Truck className='h-6 w-6 text-orange-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>
                  Active Deliveries
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  {activeDeliveries.length}
                </p>
              </div>
            </div>
          </Card>

          <Card className='p-6 shadow-lg'>
            <div className='flex items-center'>
              <div className='p-3 bg-purple-100 rounded-lg'>
                <Activity className='h-6 w-6 text-purple-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>Efficiency</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {(
                    ((readyOrders.length - filteredOrders.length) /
                      Math.max(readyOrders.length, 1)) *
                    100
                  ).toFixed(0)}
                  %
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <Card className='mb-8 shadow-lg'>
            <div className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                Filters & Search
              </h3>

              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
                {/* Order Search */}
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Search Orders
                  </label>
                  <div className='relative'>
                    <Search
                      className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
                      size={16}
                    />
                    <input
                      type='text'
                      value={orderSearchTerm}
                      onChange={(e) => setOrderSearchTerm(e.target.value)}
                      placeholder='Customer, restaurant, order ID...'
                      className='w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                    />
                  </div>
                </div>

                {/* Order Filter */}
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Order Filter
                  </label>
                  <select
                    value={orderFilter}
                    onChange={(e) => setOrderFilter(e.target.value)}
                    className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                  >
                    <option value='all'>All Orders</option>
                    <option value='high-value'>High Value (≥$500)</option>
                    <option value='urgent'>Urgent (>30 min old)</option>
                  </select>
                </div>

                {/* Agent Search */}
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Search Agents
                  </label>
                  <div className='relative'>
                    <Search
                      className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
                      size={16}
                    />
                    <input
                      type='text'
                      value={agentSearchTerm}
                      onChange={(e) => setAgentSearchTerm(e.target.value)}
                      placeholder='Name, ID, phone...'
                      className='w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                    />
                  </div>
                </div>

                {/* Agent Filter */}
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Agent Filter
                  </label>
                  <select
                    value={agentFilter}
                    onChange={(e) => setAgentFilter(e.target.value)}
                    className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                  >
                    <option value='all'>All Agents</option>
                    <option value='top-rated'>Top Rated (≥4.0★)</option>
                    <option value='nearby'>Nearby Agents</option>
                  </select>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Professional Assignment Management */}
        <div className='space-y-6 lg:space-y-8'>
          {/* Orders Assignment Table */}
          <div className='w-full'>
            <Card className='shadow-lg hover:shadow-xl transition-shadow duration-200'>
              <div className='p-4 sm:p-6'>
                <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 space-y-3 sm:space-y-0'>
                  <h2 className='text-lg sm:text-xl font-semibold text-gray-900 flex items-center'>
                    <Settings className='mr-2 flex-shrink-0' size={20} />
                    <span className='truncate'>
                      Order Assignment Management
                    </span>
                  </h2>
                  <div className='flex items-center space-x-2 sm:space-x-3 flex-shrink-0'>
                    {selectedOrdersForAssignment.length > 0 && (
                      <Badge variant='info' className='px-3 py-1'>
                        {selectedOrdersForAssignment.length} selected
                      </Badge>
                    )}
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={
                        selectedOrdersForAssignment.length > 0
                          ? clearOrderSelection
                          : selectAllOrders
                      }
                      icon={
                        selectedOrdersForAssignment.length > 0 ? (
                          <AlertCircle size={16} />
                        ) : (
                          <CheckCircle size={16} />
                        )
                      }
                    >
                      {selectedOrdersForAssignment.length > 0
                        ? "Clear All"
                        : "Select All"}
                    </Button>
                  </div>
                </div>

                <div className='overflow-x-auto -mx-4 sm:mx-0'>
                  <div className='inline-block min-w-full align-middle'>
                    <table className='min-w-full divide-y divide-gray-200'>
                      <thead className='bg-gray-50'>
                        <tr>
                          <th className='px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                            <input
                              type='checkbox'
                              checked={
                                selectedOrdersForAssignment.length ===
                                  filteredOrders.length &&
                                filteredOrders.length > 0
                              }
                              onChange={
                                selectedOrdersForAssignment.length ===
                                filteredOrders.length
                                  ? clearOrderSelection
                                  : selectAllOrders
                              }
                              className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                            />
                          </th>
                          <th className='px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                            Order Details
                          </th>
                          <th className='hidden md:table-cell px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                            Customer
                          </th>
                          <th className='hidden lg:table-cell px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                            Restaurant
                          </th>
                          <th className='px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                            Amount
                          </th>
                          <th className='hidden sm:table-cell px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                            Priority
                          </th>
                          <th className='px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                            Assign Agent
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200'>
                        {filteredOrders.map((order) => {
                          const priority = getOrderPriority(order);
                          return (
                            <tr
                              key={order.id}
                              className={`hover:bg-gray-50 ${
                                selectedOrdersForAssignment.includes(order.id)
                                  ? "bg-blue-50"
                                  : ""
                              }`}
                            >
                              <td className='px-3 sm:px-6 py-4 whitespace-nowrap'>
                                <input
                                  type='checkbox'
                                  checked={selectedOrdersForAssignment.includes(
                                    order.id
                                  )}
                                  onChange={() =>
                                    toggleOrderSelection(order.id)
                                  }
                                  className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                                />
                              </td>
                              <td className='px-3 sm:px-6 py-4'>
                                <div>
                                  <div className='text-sm font-medium text-gray-900'>
                                    Order #{order.id}
                                  </div>
                                  <div className='text-xs sm:text-sm text-gray-500'>
                                    {formatTime(order.created_at)}
                                  </div>
                                  {/* Show customer and restaurant info on mobile */}
                                  <div className='md:hidden mt-1 space-y-1'>
                                    <div className='text-xs text-gray-600'>
                                      👤 {order.customer_name}
                                    </div>
                                    <div className='lg:hidden text-xs text-gray-600'>
                                      🏪 {order.restaurant_name}
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td className='hidden md:table-cell px-3 sm:px-6 py-4 whitespace-nowrap'>
                                <div>
                                  <div className='text-sm font-medium text-gray-900'>
                                    {order.customer_name}
                                  </div>
                                  <div className='text-sm text-gray-500'>
                                    {order.customer_phone}
                                  </div>
                                </div>
                              </td>
                              <td className='hidden lg:table-cell px-3 sm:px-6 py-4 whitespace-nowrap'>
                                <div className='text-sm text-gray-900'>
                                  {order.restaurant_name}
                                </div>
                              </td>
                              <td className='px-3 sm:px-6 py-4 whitespace-nowrap'>
                                <div className='text-sm font-bold text-green-600'>
                                  {formatCurrency(order.total_amount)}
                                </div>
                              </td>
                              <td className='hidden sm:table-cell px-3 sm:px-6 py-4 whitespace-nowrap'>
                                <Badge
                                  variant={
                                    priority.color === "red"
                                      ? "danger"
                                      : priority.color === "orange"
                                      ? "warning"
                                      : priority.color === "purple"
                                      ? "info"
                                      : "secondary"
                                  }
                                  className='text-xs'
                                >
                                  {priority.level === "urgent"
                                    ? "🔥 Urgent"
                                    : priority.level === "high"
                                    ? "⚡ High"
                                    : priority.level === "high-value"
                                    ? "💎 High Value"
                                    : "Normal"}
                                </Badge>
                              </td>
                              <td className='px-3 sm:px-6 py-4'>
                                <select
                                  onChange={(e) => {
                                    if (e.target.value) {
                                      handleQuickAssign(
                                        order.id,
                                        e.target.value
                                      );
                                      e.target.value = ""; // Reset selection
                                    }
                                  }}
                                  className='text-xs sm:text-sm border border-gray-300 rounded-md px-2 sm:px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-full'
                                  disabled={assignmentLoading}
                                >
                                  <option value=''>Select Agent...</option>
                                  {filteredAgents
                                    .filter(
                                      (agent) => agent.availability === "online"
                                    )
                                    .map((agent) => (
                                      <option
                                        key={agent.id}
                                        value={agent.agent_id}
                                      >
                                        {agent.full_name} ({agent.agent_id}) -
                                        ⭐
                                        {parseFloat(agent.rating || 0).toFixed(
                                          1
                                        )}
                                      </option>
                                    ))}
                                </select>
                                {/* Show priority on mobile */}
                                <div className='sm:hidden mt-1'>
                                  <Badge
                                    variant={
                                      priority.color === "red"
                                        ? "danger"
                                        : priority.color === "orange"
                                        ? "warning"
                                        : priority.color === "purple"
                                        ? "info"
                                        : "secondary"
                                    }
                                    className='text-xs'
                                  >
                                    {priority.level === "urgent"
                                      ? "🔥 Urgent"
                                      : priority.level === "high"
                                      ? "⚡ High"
                                      : priority.level === "high-value"
                                      ? "💎 High Value"
                                      : "Normal"}
                                  </Badge>
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>

                {filteredOrders.length === 0 && (
                  <div className='text-center py-12'>
                    <Package className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                    <p className='text-gray-500 text-lg'>
                      {readyOrders.length === 0
                        ? "No orders ready for assignment"
                        : "No orders match your filters"}
                    </p>
                    {readyOrders.length > 0 && filteredOrders.length === 0 && (
                      <Button
                        variant='outline'
                        onClick={() => {
                          setOrderSearchTerm("");
                          setOrderFilter("all");
                        }}
                        className='mt-3'
                      >
                        Clear Filters
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Agents Management Panel */}
          <div className='w-full'>
            <Card className='shadow-lg hover:shadow-xl transition-shadow duration-200'>
              <div className='p-4 sm:p-6'>
                <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 space-y-3 sm:space-y-0'>
                  <h2 className='text-base sm:text-lg font-semibold text-gray-900 flex items-center'>
                    <Users className='mr-2 flex-shrink-0' size={18} />
                    <span className='truncate'>Delivery Agents</span>
                  </h2>
                  <div className='flex items-center bg-gray-100 rounded-lg p-1 text-xs sm:text-sm'>
                    <button
                      onClick={() => setAgentViewMode("available")}
                      className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                        agentViewMode === "available"
                          ? "bg-white text-green-600 shadow-sm"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      Available
                    </button>
                    <button
                      onClick={() => setAgentViewMode("offline")}
                      className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                        agentViewMode === "offline"
                          ? "bg-white text-red-600 shadow-sm"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      Offline
                    </button>
                    <button
                      onClick={() => setAgentViewMode("all")}
                      className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                        agentViewMode === "all"
                          ? "bg-white text-blue-600 shadow-sm"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      All
                    </button>
                  </div>
                </div>

                {/* Agent Statistics */}
                <div className='grid grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6'>
                  <div className='bg-green-50 p-3 sm:p-4 rounded-lg border border-green-200 hover:bg-green-100 transition-colors duration-200'>
                    <div className='flex items-center'>
                      <div className='p-2 bg-green-100 rounded-lg'>
                        <Activity className='h-4 w-4 text-green-600' />
                      </div>
                      <div className='ml-3'>
                        <p className='text-sm font-medium text-green-800'>
                          Available
                        </p>
                        <p className='text-lg font-bold text-green-900'>
                          {
                            availableAgents.filter(
                              (agent) => agent.availability === "online"
                            ).length
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className='bg-red-50 p-4 rounded-lg border border-red-200'>
                    <div className='flex items-center'>
                      <div className='p-2 bg-red-100 rounded-lg'>
                        <AlertCircle className='h-4 w-4 text-red-600' />
                      </div>
                      <div className='ml-3'>
                        <p className='text-sm font-medium text-red-800'>
                          Offline
                        </p>
                        <p className='text-lg font-bold text-red-900'>
                          {
                            availableAgents.filter(
                              (agent) => agent.availability === "offline"
                            ).length
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Agents List */}
                <div className='space-y-3 max-h-96 overflow-y-auto'>
                  {availableAgents
                    .filter((agent) => {
                      if (agentViewMode === "available")
                        return agent.availability === "online";
                      if (agentViewMode === "offline")
                        return agent.availability === "offline";
                      return true; // all
                    })
                    .map((agent) => (
                      <div
                        key={agent.id}
                        className={`border-2 rounded-lg p-4 transition-all duration-200 ${
                          agent.availability === "online"
                            ? "border-green-200 bg-green-50 hover:border-green-300"
                            : "border-red-200 bg-red-50 hover:border-red-300"
                        }`}
                      >
                        <div className='flex items-start justify-between'>
                          <div className='flex-1'>
                            <div className='flex items-center justify-between mb-2'>
                              <h3 className='font-semibold text-gray-900 text-sm'>
                                {agent.full_name}
                              </h3>
                              <Badge
                                variant={
                                  agent.availability === "online"
                                    ? "success"
                                    : "danger"
                                }
                                className='text-xs'
                              >
                                {agent.availability === "online" ? (
                                  <>
                                    <Activity className='w-3 h-3 mr-1' />
                                    Online
                                  </>
                                ) : (
                                  <>
                                    <AlertCircle className='w-3 h-3 mr-1' />
                                    Offline
                                  </>
                                )}
                              </Badge>
                            </div>

                            <div className='space-y-1 text-xs text-gray-600'>
                              <div className='flex items-center justify-between'>
                                <span>ID: {agent.agent_id}</span>
                                <div className='flex items-center'>
                                  <Star className='w-3 h-3 text-yellow-500 fill-current mr-1' />
                                  <span className='font-medium'>
                                    {parseFloat(agent.rating || 0).toFixed(1)}
                                  </span>
                                </div>
                              </div>
                              <div className='flex items-center justify-between'>
                                <span>{agent.phone_number}</span>
                                <span className='font-medium text-blue-600'>
                                  {agent.total_deliveries} deliveries
                                </span>
                              </div>
                              {agent.employment_status && (
                                <div className='text-xs text-gray-500'>
                                  Status: {agent.employment_status}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>

                {availableAgents.filter((agent) => {
                  if (agentViewMode === "available")
                    return agent.availability === "online";
                  if (agentViewMode === "offline")
                    return agent.availability === "offline";
                  return true;
                }).length === 0 && (
                  <div className='text-center py-8'>
                    <Users className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                    <p className='text-gray-500'>
                      No {agentViewMode === "all" ? "" : agentViewMode} agents
                      found
                    </p>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>

        {/* Bulk Assignment Panel */}
        {selectedOrdersForAssignment.length > 0 && (
          <Card className='mt-8 shadow-lg border-2 border-blue-200'>
            <div className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4 flex items-center'>
                <Users className='mr-2 h-5 w-5 text-blue-600' />
                Bulk Assignment ({selectedOrdersForAssignment.length} orders
                selected)
              </h3>

              <div className='bg-gradient-to-r from-blue-50 to-green-50 p-6 rounded-lg border border-blue-200'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                  {/* Selected Orders Summary */}
                  <div>
                    <h4 className='text-sm font-medium text-gray-700 mb-3'>
                      Selected Orders
                    </h4>
                    <div className='bg-white rounded-lg p-4 border border-blue-100 max-h-48 overflow-y-auto'>
                      {selectedOrdersForAssignment.map((orderId) => {
                        const order = filteredOrders.find(
                          (o) => o.id === orderId
                        );
                        return order ? (
                          <div
                            key={orderId}
                            className='flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0'
                          >
                            <div>
                              <span className='text-sm font-medium text-gray-900'>
                                Order #{order.id}
                              </span>
                              <span className='text-xs text-gray-500 ml-2'>
                                {order.customer_name}
                              </span>
                            </div>
                            <span className='text-sm font-bold text-green-600'>
                              {formatCurrency(order.total_amount)}
                            </span>
                          </div>
                        ) : null;
                      })}
                    </div>
                    <div className='mt-2 text-sm text-gray-600'>
                      Total Value:{" "}
                      <span className='font-bold text-green-600'>
                        {formatCurrency(
                          selectedOrdersForAssignment.reduce(
                            (total, orderId) => {
                              const order = filteredOrders.find(
                                (o) => o.id === orderId
                              );
                              return (
                                total +
                                (order ? parseFloat(order.total_amount) : 0)
                              );
                            },
                            0
                          )
                        )}
                      </span>
                    </div>
                  </div>

                  {/* Agent Selection */}
                  <div>
                    <h4 className='text-sm font-medium text-gray-700 mb-3'>
                      Assign to Agent
                    </h4>
                    <div className='space-y-3'>
                      <select
                        value={selectedAgent?.agent_id || ""}
                        onChange={(e) => {
                          const agent = filteredAgents.find(
                            (a) => a.agent_id === e.target.value
                          );
                          setSelectedAgent(agent || null);
                        }}
                        className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                      >
                        <option value=''>Select an agent...</option>
                        {filteredAgents
                          .filter((agent) => agent.availability === "online")
                          .map((agent) => (
                            <option key={agent.id} value={agent.agent_id}>
                              {agent.full_name} ({agent.agent_id}) - ⭐
                              {parseFloat(agent.rating || 0).toFixed(1)} -{" "}
                              {agent.total_deliveries} deliveries
                            </option>
                          ))}
                      </select>

                      {selectedAgent && (
                        <div className='bg-white rounded-lg p-4 border border-green-100'>
                          <div className='flex items-center space-x-3'>
                            <div className='p-2 bg-green-100 rounded-lg'>
                              <User className='h-5 w-5 text-green-600' />
                            </div>
                            <div>
                              <p className='text-sm font-medium text-gray-900'>
                                {selectedAgent.full_name}
                              </p>
                              <p className='text-xs text-gray-500'>
                                {selectedAgent.agent_id} •{" "}
                                {selectedAgent.phone_number}
                              </p>
                              <div className='flex items-center mt-1'>
                                <Star className='w-3 h-3 text-yellow-500 fill-current mr-1' />
                                <span className='text-xs font-medium'>
                                  {parseFloat(
                                    selectedAgent.rating || 0
                                  ).toFixed(1)}
                                </span>
                                <span className='text-xs text-gray-500 ml-2'>
                                  • {selectedAgent.total_deliveries} deliveries
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className='flex space-x-3'>
                        <Button
                          variant='outline'
                          onClick={clearOrderSelection}
                          className='flex-1'
                          icon={<AlertCircle size={16} />}
                        >
                          Clear Selection
                        </Button>
                        <Button
                          onClick={() => {
                            if (selectedAgent) {
                              // Assign all selected orders to the selected agent
                              selectedOrdersForAssignment.forEach((orderId) => {
                                handleQuickAssign(
                                  orderId,
                                  selectedAgent.agent_id
                                );
                              });
                              clearOrderSelection();
                              setSelectedAgent(null);
                            }
                          }}
                          disabled={!selectedAgent || assignmentLoading}
                          loading={assignmentLoading}
                          className='flex-1 bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white'
                          icon={<CheckCircle size={16} />}
                        >
                          Assign All Orders
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ManualOrderAssignment;
