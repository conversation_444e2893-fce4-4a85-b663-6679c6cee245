from django.urls import path
from . import views
from .employee_views import (
    AdminEmployeeCreateView, AdminEmployeeListView, AdminEmployeeDetailView
)
from .manual_assignment_views import (
    AdminOrderAssignmentView, AdminOrderReassignmentView, AdminActiveDeliveriesView
)
# Registration imports
from .registration_views import AgentSelfRegistrationView, ApplicationStatusView, AgentStatusUpdateView
from .admin_approval_views import (
    AdminAgentApprovalView, AgentApprovalActionView,
    AgentDetailView, BulkApprovalView
)

app_name = 'delivery_agent'

urlpatterns = [
    # Employee Management URLs (New System)
    path('admin/employees/', AdminEmployeeListView.as_view(), name='admin_employee_list'),
    path('admin/employees/create/', AdminEmployeeCreateView.as_view(), name='admin_employee_create'),
    path('admin/employees/<str:agent_id>/', AdminEmployeeDetailView.as_view(), name='admin_employee_detail'),

    # Manual Order Assignment URLs (New System)
    path('admin/assignments/', AdminOrderAssignmentView.as_view(), name='admin_order_assignments'),
    path('admin/assignments/reassign/', AdminOrderReassignmentView.as_view(), name='admin_order_reassignment'),
    path('admin/active-deliveries/', AdminActiveDeliveriesView.as_view(), name='admin_active_deliveries'),

    # Employee Dashboard and Operations
    path('dashboard/', views.DeliveryAgentDashboardView.as_view(), name='delivery-agent-dashboard'),
    path('profile/', views.DeliveryAgentProfileView.as_view(), name='delivery-agent-profile'),
    path('clock-in/', views.ClockInView.as_view(), name='clock-in'),
    path('clock-out/', views.ClockOutView.as_view(), name='clock-out'),
    path('set-break/', views.SetBreakView.as_view(), name='set-break'),
    path('return-from-break/', views.ReturnFromBreakView.as_view(), name='return-from-break'),

    # Status Management
    path('toggle-online-status/', views.ToggleOnlineStatusView.as_view(), name='toggle-online-status'),
    path('update-availability/', views.UpdateAvailabilityView.as_view(), name='update-availability'),

    # Shift Management
    path('start-shift/', views.StartShiftView.as_view(), name='start-shift'),
    path('end-shift/', views.EndShiftView.as_view(), name='end-shift'),

    # Order Management
    path('my-orders/', views.MyOrdersView.as_view(), name='my-orders'),
    path('available-orders/', views.AvailableOrdersView.as_view(), name='available-orders'),
    path('accept-order/', views.AcceptOrderView.as_view(), name='accept-order'),
    path('reject-order/', views.RejectOrderView.as_view(), name='reject-order'),
    path('update-order-status/', views.UpdateOrderStatusView.as_view(), name='update-order-status'),
    path('collect-cash/', views.CollectCashView.as_view(), name='collect-cash'),

    # Performance and Earnings
    path('earnings-summary/', views.EarningsSummaryView.as_view(), name='earnings-summary'),
    path('performance-metrics/', views.PerformanceMetricsView.as_view(), name='performance-metrics'),

    # Registration URLs (Re-enabled)
    path('register/', AgentSelfRegistrationView.as_view(), name='delivery_agent_register_page'),
    path('self-register/', AgentSelfRegistrationView.as_view(), name='agent_self_registration'),
    path('application-status/', ApplicationStatusView.as_view(), name='application_status'),
    path('update-status/', AgentStatusUpdateView.as_view(), name='agent_status_update'),

    # Legacy URLs (To be removed after migration)
    path('available-orders/', views.AvailableOrdersView.as_view(), name='available-orders'),
    path('accept-order/', views.AcceptOrderView.as_view(), name='accept-order'),
    path('admin/applications/', AdminAgentApprovalView.as_view(), name='admin_agent_applications'),
    path('admin/applications/<str:agent_id>/action/', AgentApprovalActionView.as_view(), name='agent_approval_action'),
    path('admin/applications/<str:agent_id>/detail/', AgentDetailView.as_view(), name='agent_detail'),
    path('admin/bulk-approval/', BulkApprovalView.as_view(), name='bulk_approval'),
]
