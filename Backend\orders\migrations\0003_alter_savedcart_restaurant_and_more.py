# Generated by Django 5.0.4 on 2025-05-21 09:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0002_savedcart_savedcartitem'),
        ('restaurant', '0007_restaurant_delivery_fee'),
    ]

    operations = [
        migrations.AlterField(
            model_name='savedcart',
            name='restaurant',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='restaurant.restaurant'),
        ),
        migrations.AlterUniqueTogether(
            name='savedcartitem',
            unique_together={('cart', 'menu_item')},
        ),
    ]
