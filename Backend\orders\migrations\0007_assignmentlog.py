# Generated by Django 5.0.4 on 2025-05-28 10:40

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0006_rejectionlog'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AssignmentLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('assigned', 'Assigned'), ('accepted', 'Accepted'), ('rejected', 'Rejected'), ('picked_up', 'Picked Up'), ('delivered', 'Delivered')], max_length=20)),
                ('reason', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assignment_actions', to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignment_logs', to='orders.order')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['order', 'action'], name='orders_assi_order_i_11b754_idx')],
            },
        ),
    ]
