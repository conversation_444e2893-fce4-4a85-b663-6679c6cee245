import React, { useState, useEffect } from 'react';
import { 
  DollarSign, 
  Package, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  Calculator,
  Banknote,
  Receipt,
  TrendingUp
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { deliveryAgentApi } from '../../services/deliveryAgentApi';
import toast from 'react-hot-toast';

const CashCollectionDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [cashOrders, setCashOrders] = useState([]);
  const [totalCashCollected, setTotalCashCollected] = useState(0);
  const [pendingCashOrders, setPendingCashOrders] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch cash collection data
  const fetchCashData = async () => {
    try {
      setRefreshing(true);
      
      // Get orders that require cash collection
      const ordersResponse = await deliveryAgentApi.getMyOrders({
        payment_method: 'cash',
        status: 'delivered'
      });
      
      if (ordersResponse.data?.status === 'success') {
        const orders = ordersResponse.data.data || [];
        setCashOrders(orders);
        
        // Calculate total cash collected today
        const today = new Date().toDateString();
        const todayOrders = orders.filter(order => 
          new Date(order.delivered_at || order.updated_at).toDateString() === today
        );
        
        const totalCash = todayOrders.reduce((sum, order) => 
          sum + parseFloat(order.total_amount || 0), 0
        );
        setTotalCashCollected(totalCash);
        
        // Get pending cash collection orders
        const pending = orders.filter(order => !order.cash_collected);
        setPendingCashOrders(pending);
      }
    } catch (error) {
      console.error('Error fetching cash data:', error);
      toast.error('Failed to load cash collection data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Mark cash as collected
  const markCashCollected = async (orderId, amount) => {
    try {
      const response = await deliveryAgentApi.collectCash(orderId, amount);
      
      if (response.data?.status === 'success') {
        toast.success('Cash collection recorded successfully');
        fetchCashData(); // Refresh data
      }
    } catch (error) {
      console.error('Error recording cash collection:', error);
      toast.error('Failed to record cash collection');
    }
  };

  useEffect(() => {
    fetchCashData();
    
    // Refresh every 2 minutes
    const interval = setInterval(fetchCashData, 120000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading cash collection data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="mb-4 md:mb-0">
              <h1 className="text-2xl font-bold text-gray-900">Cash Collection</h1>
              <p className="text-gray-600">Track and manage cash collections from deliveries</p>
            </div>
            
            <button
              onClick={fetchCashData}
              disabled={refreshing}
              className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Today's Cash Collected</p>
                <p className="text-2xl font-bold text-gray-900">
                  AFN {totalCashCollected.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Package className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Collections</p>
                <p className="text-2xl font-bold text-gray-900">
                  {pendingCashOrders.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Receipt className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Cash Orders</p>
                <p className="text-2xl font-bold text-gray-900">
                  {cashOrders.length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Pending Cash Collections */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Pending Cash Collections</h2>
          
          {pendingCashOrders.length === 0 ? (
            <div className="text-center py-12">
              <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">All cash collected!</h3>
              <p className="text-gray-600">No pending cash collections at the moment.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingCashOrders.map((order) => (
                <CashOrderCard 
                  key={order.id} 
                  order={order} 
                  onCollect={markCashCollected}
                />
              ))}
            </div>
          )}
        </div>

        {/* Recent Cash Collections */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Recent Cash Collections</h2>
          
          <div className="space-y-4">
            {cashOrders
              .filter(order => order.cash_collected)
              .slice(0, 10)
              .map((order) => (
                <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900">Order #{order.id}</h3>
                      <p className="text-sm text-gray-600">
                        Collected: {new Date(order.cash_collected_at).toLocaleString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-green-600">AFN {order.total_amount}</p>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Collected
                      </span>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Cash Order Card Component
const CashOrderCard = ({ order, onCollect }) => {
  const [collecting, setCollecting] = useState(false);

  const handleCollect = async () => {
    setCollecting(true);
    try {
      await onCollect(order.id, order.total_amount);
    } finally {
      setCollecting(false);
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="font-semibold text-gray-900">Order #{order.id}</h3>
          <p className="text-sm text-gray-600">
            Customer: {order.customer_name}
          </p>
          <p className="text-sm text-gray-600">
            <Clock className="h-4 w-4 inline mr-1" />
            Delivered: {new Date(order.delivered_at || order.updated_at).toLocaleString()}
          </p>
        </div>
        <div className="text-right">
          <p className="text-2xl font-bold text-gray-900">AFN {order.total_amount}</p>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <AlertCircle className="h-3 w-3 mr-1" />
            Pending Collection
          </span>
        </div>
      </div>

      <button
        onClick={handleCollect}
        disabled={collecting}
        className="w-full flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50"
      >
        <Banknote className="h-4 w-4 mr-2" />
        {collecting ? 'Recording...' : 'Mark as Collected'}
      </button>
    </div>
  );
};

export default CashCollectionDashboard;
