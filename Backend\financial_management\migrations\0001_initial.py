# Generated by Django 5.0.4 on 2025-07-13 04:01

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('orders', '0011_remove_assignmentlog_notes'),
        ('restaurant', '0013_promotion_promotioncode_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CommissionStructure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission_rate', models.DecimalField(decimal_places=2, default=15.0, help_text='Commission percentage (0-50%)', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(50)])),
                ('payment_processing_fee', models.DecimalField(decimal_places=2, default=2.5, help_text='Payment processing fee percentage', max_digits=5)),
                ('delivery_fee_share', models.DecimalField(decimal_places=2, default=30.0, help_text="Platform's share of delivery fee percentage", max_digits=5)),
                ('minimum_payout_amount', models.DecimalField(decimal_places=2, default=50.0, help_text='Minimum amount for payout', max_digits=8)),
                ('payout_frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('bi_weekly', 'Bi-Weekly'), ('monthly', 'Monthly')], default='weekly', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('restaurant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='commission_structure', to='restaurant.restaurant')),
            ],
        ),
        migrations.CreateModel(
            name='RestaurantBankAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bank_name', models.CharField(max_length=100)),
                ('account_holder_name', models.CharField(max_length=100)),
                ('account_number', models.CharField(max_length=50)),
                ('routing_number', models.CharField(blank=True, max_length=20)),
                ('swift_code', models.CharField(blank=True, max_length=20)),
                ('bank_address', models.TextField()),
                ('is_verified', models.BooleanField(default=False)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('restaurant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='bank_account', to='restaurant.restaurant')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_bank_accounts', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='RestaurantEarnings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('delivery_fee', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('commission_amount', models.DecimalField(decimal_places=2, max_digits=8)),
                ('payment_processing_fee', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('delivery_fee_platform_share', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('gross_earnings', models.DecimalField(decimal_places=2, max_digits=10)),
                ('net_earnings', models.DecimalField(decimal_places=2, max_digits=10)),
                ('is_paid', models.BooleanField(default=False)),
                ('payout_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='earnings', to='orders.order')),
                ('restaurant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='earnings', to='restaurant.restaurant')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RestaurantPayout',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payout_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('payout_period_start', models.DateTimeField()),
                ('payout_period_end', models.DateTimeField()),
                ('orders_count', models.PositiveIntegerField()),
                ('total_gross_earnings', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total_commission', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total_fees', models.DecimalField(decimal_places=2, max_digits=12)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('payment_method', models.CharField(choices=[('bank_transfer', 'Bank Transfer'), ('paypal', 'PayPal'), ('stripe', 'Stripe'), ('check', 'Check')], default='bank_transfer', max_length=50)),
                ('transaction_id', models.CharField(blank=True, max_length=100)),
                ('payment_reference', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('restaurant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payouts', to='restaurant.restaurant')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FinancialReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_type', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], max_length=20)),
                ('period_start', models.DateTimeField()),
                ('period_end', models.DateTimeField()),
                ('total_orders', models.PositiveIntegerField(default=0)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_commission', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_fees', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('net_earnings', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('average_order_value', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('commission_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('restaurant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='financial_reports', to='restaurant.restaurant')),
            ],
            options={
                'ordering': ['-period_start'],
                'unique_together': {('restaurant', 'report_type', 'period_start')},
            },
        ),
    ]
