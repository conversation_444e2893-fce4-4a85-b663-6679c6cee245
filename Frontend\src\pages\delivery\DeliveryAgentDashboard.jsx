import React, { useState, useEffect } from "react";
import {
  Power,
  PowerOff,
  Clock,
  DollarSign,
  Package,
  MapPin,
  Star,
  TrendingUp,
  Activity,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Timer,
  Navigation,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";
import toast from "react-hot-toast";

const DeliveryAgentDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [isOnline, setIsOnline] = useState(false);
  const [isClockedIn, setIsClockedIn] = useState(false);
  const [availability, setAvailability] = useState("offline");
  const [assignedOrders, setAssignedOrders] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      setRefreshing(true);
      const response = await deliveryAgentApi.getDashboard();

      if (response.data.status === "success") {
        const data = response.data.data;
        setDashboardData(data);
        setIsOnline(data.agent_info?.is_online || false);
        setIsClockedIn(data.agent_info?.is_clocked_in || false);
        setAvailability(data.agent_info?.availability || "offline");
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      toast.error("Failed to load dashboard data");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch assigned orders
  const fetchAssignedOrders = async () => {
    try {
      const response = await deliveryAgentApi.getAssignedOrders();
      if (response.data.status === "success") {
        setAssignedOrders(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
    }
  };

  // Toggle online status
  const toggleOnlineStatus = async () => {
    try {
      const response = await deliveryAgentApi.toggleOnlineStatus();
      if (response.data.status === "success") {
        const newStatus = response.data.data.is_online;
        setIsOnline(newStatus);
        setAvailability(response.data.data.availability);
        toast.success(`You are now ${newStatus ? "online" : "offline"}`);
        fetchDashboardData();
      }
    } catch (error) {
      console.error("Error toggling online status:", error);
      toast.error("Failed to update online status");
    }
  };

  // Clock in/out
  const toggleClockStatus = async () => {
    try {
      const endpoint = isClockedIn ? "clock-out" : "clock-in";
      const response = await deliveryAgentApi.clockInOut(endpoint);

      if (response.data.status === "success") {
        setIsClockedIn(!isClockedIn);
        toast.success(response.data.message);
        fetchDashboardData();
      }
    } catch (error) {
      console.error("Error toggling clock status:", error);
      toast.error("Failed to update clock status");
    }
  };

  // Update availability
  const updateAvailability = async (newAvailability) => {
    try {
      const response = await deliveryAgentApi.updateAvailability({
        availability: newAvailability,
      });

      if (response.data.status === "success") {
        setAvailability(newAvailability);
        setIsOnline(response.data.data.is_online);
        toast.success("Availability updated successfully");
        fetchDashboardData();
      }
    } catch (error) {
      console.error("Error updating availability:", error);
      toast.error("Failed to update availability");
    }
  };

  useEffect(() => {
    fetchDashboardData();
    fetchAssignedOrders();

    // Refresh data every 30 seconds
    const interval = setInterval(() => {
      fetchDashboardData();
      fetchAssignedOrders();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <RefreshCw className='h-8 w-8 animate-spin text-blue-600 mx-auto mb-4' />
          <p className='text-gray-600'>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  const agentInfo = dashboardData?.agent_info || {};
  const todayStats = dashboardData?.today_stats || {};

  return (
    <div className='min-h-screen bg-gray-50 p-4'>
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <div className='bg-white rounded-lg shadow-sm p-6 mb-6'>
          <div className='flex flex-col md:flex-row md:items-center md:justify-between'>
            <div className='mb-4 md:mb-0'>
              <h1 className='text-2xl font-bold text-gray-900'>
                Welcome, {agentInfo.full_name || user?.name}
              </h1>
              <p className='text-gray-600'>Agent ID: {agentInfo.agent_id}</p>
            </div>

            <div className='flex items-center space-x-4'>
              <button
                onClick={() => {
                  fetchDashboardData();
                  fetchAssignedOrders();
                }}
                disabled={refreshing}
                className='flex items-center px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors'
              >
                <RefreshCw
                  className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
                />
                Refresh
              </button>

              <div
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  isOnline
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {isOnline ? "Online" : "Offline"}
              </div>
            </div>
          </div>
        </div>

        {/* Status Controls */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-6'>
          {/* Online/Offline Toggle */}
          <div className='bg-white rounded-lg shadow-sm p-6'>
            <h3 className='text-lg font-semibold mb-4'>Online Status</h3>
            <button
              onClick={toggleOnlineStatus}
              className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-colors ${
                isOnline
                  ? "bg-red-600 hover:bg-red-700 text-white"
                  : "bg-green-600 hover:bg-green-700 text-white"
              }`}
            >
              {isOnline ? (
                <>
                  <PowerOff className='h-5 w-5 mr-2' />
                  Go Offline
                </>
              ) : (
                <>
                  <Power className='h-5 w-5 mr-2' />
                  Go Online
                </>
              )}
            </button>
          </div>

          {/* Clock In/Out */}
          <div className='bg-white rounded-lg shadow-sm p-6'>
            <h3 className='text-lg font-semibold mb-4'>Work Shift</h3>
            <button
              onClick={toggleClockStatus}
              className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-colors ${
                isClockedIn
                  ? "bg-orange-600 hover:bg-orange-700 text-white"
                  : "bg-blue-600 hover:bg-blue-700 text-white"
              }`}
            >
              <Clock className='h-5 w-5 mr-2' />
              {isClockedIn ? "Clock Out" : "Clock In"}
            </button>
          </div>

          {/* Availability Status */}
          <div className='bg-white rounded-lg shadow-sm p-6'>
            <h3 className='text-lg font-semibold mb-4'>Availability</h3>
            <select
              value={availability}
              onChange={(e) => updateAvailability(e.target.value)}
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            >
              <option value='offline'>Offline</option>
              <option value='available'>Available</option>
              <option value='busy'>Busy</option>
              <option value='break'>On Break</option>
            </select>
          </div>
        </div>

        {/* Stats Cards */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6'>
          <div className='bg-white rounded-lg shadow-sm p-6'>
            <div className='flex items-center'>
              <div className='p-2 bg-blue-100 rounded-lg'>
                <Package className='h-6 w-6 text-blue-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>
                  Today's Deliveries
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  {todayStats.deliveries_completed || 0}
                </p>
              </div>
            </div>
          </div>

          <div className='bg-white rounded-lg shadow-sm p-6'>
            <div className='flex items-center'>
              <div className='p-2 bg-green-100 rounded-lg'>
                <DollarSign className='h-6 w-6 text-green-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>
                  Today's Earnings
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  AFN {todayStats.earnings || "0.00"}
                </p>
              </div>
            </div>
          </div>

          <div className='bg-white rounded-lg shadow-sm p-6'>
            <div className='flex items-center'>
              <div className='p-2 bg-yellow-100 rounded-lg'>
                <Star className='h-6 w-6 text-yellow-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>Rating</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {agentInfo.rating || "0.0"}
                </p>
              </div>
            </div>
          </div>

          <div className='bg-white rounded-lg shadow-sm p-6'>
            <div className='flex items-center'>
              <div className='p-2 bg-purple-100 rounded-lg'>
                <TrendingUp className='h-6 w-6 text-purple-600' />
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-gray-600'>
                  Completion Rate
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  {todayStats.completion_rate || 0}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Assigned Orders */}
        <div className='bg-white rounded-lg shadow-sm p-6'>
          <div className='flex items-center justify-between mb-6'>
            <h2 className='text-xl font-bold text-gray-900'>Assigned Orders</h2>
            <span className='bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-0.5 rounded'>
              {assignedOrders.length} orders
            </span>
          </div>

          {assignedOrders.length === 0 ? (
            <div className='text-center py-12'>
              <Package className='h-12 w-12 text-gray-400 mx-auto mb-4' />
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                No orders assigned
              </h3>
              <p className='text-gray-600'>
                {isOnline
                  ? "You're online and ready to receive orders!"
                  : "Go online to start receiving orders"}
              </p>
            </div>
          ) : (
            <div className='space-y-4'>
              {assignedOrders.map((order) => (
                <OrderCard
                  key={order.id}
                  order={order}
                  onUpdate={fetchAssignedOrders}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Order Card Component
const OrderCard = ({ order, onUpdate }) => {
  const [updating, setUpdating] = useState(false);

  const updateOrderStatus = async (newStatus) => {
    try {
      setUpdating(true);
      const response = await deliveryAgentApi.updateOrderStatus(order.id, {
        status: newStatus,
      });

      if (response.data.status === "success") {
        toast.success(`Order ${newStatus} successfully`);
        onUpdate();
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error("Failed to update order status");
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "assigned":
        return "bg-blue-100 text-blue-800";
      case "picked_up":
        return "bg-yellow-100 text-yellow-800";
      case "in_transit":
        return "bg-purple-100 text-purple-800";
      case "delivered":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getNextAction = (status) => {
    switch (status) {
      case "assigned":
        return {
          action: "picked_up",
          label: "Mark as Picked Up",
          icon: CheckCircle,
        };
      case "picked_up":
        return {
          action: "in_transit",
          label: "Start Delivery",
          icon: Navigation,
        };
      case "in_transit":
        return {
          action: "delivered",
          label: "Mark as Delivered",
          icon: CheckCircle,
        };
      default:
        return null;
    }
  };

  const nextAction = getNextAction(order.status);

  return (
    <div className='border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow'>
      <div className='flex items-start justify-between mb-4'>
        <div>
          <div className='flex items-center space-x-2 mb-2'>
            <h3 className='font-semibold text-gray-900'>Order #{order.id}</h3>
            <span
              className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                order.status
              )}`}
            >
              {order.status.replace("_", " ").toUpperCase()}
            </span>
          </div>
          <p className='text-sm text-gray-600'>
            <MapPin className='h-4 w-4 inline mr-1' />
            {order.delivery_address?.street || "Address not available"}
          </p>
        </div>
        <div className='text-right'>
          <p className='font-semibold text-gray-900'>
            AFN {order.total_amount}
          </p>
          <p className='text-sm text-gray-600'>
            <Timer className='h-4 w-4 inline mr-1' />
            {new Date(order.created_at).toLocaleTimeString()}
          </p>
        </div>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
        <div>
          <p className='text-sm font-medium text-gray-700'>Restaurant</p>
          <p className='text-sm text-gray-600'>{order.restaurant_name}</p>
        </div>
        <div>
          <p className='text-sm font-medium text-gray-700'>Customer</p>
          <p className='text-sm text-gray-600'>{order.customer_name}</p>
        </div>
      </div>

      {nextAction && (
        <div className='flex space-x-2'>
          <button
            onClick={() => updateOrderStatus(nextAction.action)}
            disabled={updating}
            className='flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50'
          >
            <nextAction.icon className='h-4 w-4 mr-2' />
            {updating ? "Updating..." : nextAction.label}
          </button>

          {order.status === "assigned" && (
            <button
              onClick={() => updateOrderStatus("cancelled")}
              disabled={updating}
              className='flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50'
            >
              <AlertCircle className='h-4 w-4 mr-2' />
              Cancel
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default DeliveryAgentDashboard;
