# Generated by Django 5.0.4 on 2025-07-22 12:08

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DeliveryAgentProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('agent_id', models.CharField(help_text='Unique agent identifier (e.g., DA12345678)', max_length=20, unique=True)),
                ('full_name', models.CharField(help_text='نوم (Full Name)', max_length=200)),
                ('father_name', models.CharField(help_text="د پلار نوم (Father's Name)", max_length=200)),
                ('national_id', models.Char<PERSON><PERSON>(help_text='د تذکرې شمیره (National ID)', max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^\\d{10,15}$', 'د تذکرې شمیره باید ۱۰-۱۵ عدد وي')])),
                ('date_of_birth', models.DateField(blank=True, help_text='د زیږیدو نیټه (Date of Birth)', null=True)),
                ('gender', models.CharField(choices=[('male', 'نارینه (Male)'), ('female', 'ښځینه (Female)')], default='male', max_length=10)),
                ('marital_status', models.CharField(choices=[('single', 'مجرد (Single)'), ('married', 'واده شوی (Married)'), ('divorced', 'طلاق شوی (Divorced)'), ('widowed', 'کونډه (Widowed)')], default='single', max_length=10)),
                ('phone_number', models.CharField(help_text='اصلي تلیفون شمیره (Primary Phone)', max_length=20, validators=[django.core.validators.RegexValidator('^\\+93\\d{9}$', 'د تلیفون شمیره باید +93 څخه پیل شي')])),
                ('secondary_phone', models.CharField(blank=True, help_text='دویمه تلیفون شمیره (Secondary Phone)', max_length=20, validators=[django.core.validators.RegexValidator('^\\+93\\d{9}$', 'د تلیفون شمیره باید +93 څخه پیل شي')])),
                ('email', models.EmailField(blank=True, help_text='بریښنالیک (Email)', max_length=254)),
                ('province', models.CharField(choices=[('kabul', 'کابل (Kabul)'), ('herat', 'هرات (Herat)'), ('kandahar', 'کندهار (Kandahar)'), ('balkh', 'بلخ (Balkh)'), ('nangarhar', 'ننګرهار (Nangarhar)'), ('kunduz', 'کندز (Kunduz)'), ('takhar', 'تخار (Takhar)'), ('baghlan', 'بغلان (Baghlan)'), ('ghazni', 'غزني (Ghazni)'), ('paktia', 'پکتیا (Paktia)'), ('khost', 'خوست (Khost)'), ('laghman', 'لغمان (Laghman)'), ('kapisa', 'کاپیسا (Kapisa)'), ('parwan', 'پروان (Parwan)'), ('wardak', 'وردک (Wardak)'), ('logar', 'لوګر (Logar)'), ('bamyan', 'بامیان (Bamyan)'), ('panjshir', 'پنجشیر (Panjshir)'), ('badakhshan', 'بدخشان (Badakhshan)'), ('faryab', 'فاریاب (Faryab)'), ('jawzjan', 'جوزجان (Jawzjan)'), ('sar_e_pol', 'سرپل (Sar-e Pol)'), ('samangan', 'سمنګان (Samangan)'), ('badghis', 'بادغیس (Badghis)'), ('ghor', 'غور (Ghor)'), ('daykundi', 'دایکندي (Daykundi)'), ('uruzgan', 'ارزګان (Uruzgan)'), ('zabul', 'زابل (Zabul)'), ('paktika', 'پکتیکا (Paktika)'), ('kunar', 'کنړ (Kunar)'), ('nuristan', 'نورستان (Nuristan)'), ('farah', 'فراه (Farah)'), ('nimroz', 'نیمروز (Nimroz)'), ('helmand', 'هلمند (Helmand)')], help_text='ولایت (Province)', max_length=50)),
                ('district', models.CharField(help_text='ولسوالۍ (District)', max_length=100)),
                ('area', models.CharField(help_text='سیمه (Area)', max_length=100)),
                ('street_address', models.TextField(help_text='د کوڅې پته (Street Address)')),
                ('nearby_landmark', models.CharField(blank=True, help_text='نږدې ځای (Nearby Landmark)', max_length=200)),
                ('vehicle_type', models.CharField(choices=[('motorcycle', 'موټرسایکل (Motorcycle)'), ('bicycle', 'بایسکل (Bicycle)'), ('car', 'موټر (Car)'), ('rickshaw', 'رکشا (Rickshaw)'), ('scooter', 'سکوټر (Scooter)')], max_length=20)),
                ('vehicle_model', models.CharField(blank=True, help_text='د وسایطو ماډل (Vehicle Model)', max_length=100)),
                ('vehicle_year', models.IntegerField(blank=True, help_text='د وسایطو کال (Vehicle Year)', null=True, validators=[django.core.validators.MinValueValidator(1990), django.core.validators.MaxValueValidator(2030)])),
                ('license_plate', models.CharField(blank=True, help_text='د پلیټ شمیره (License Plate)', max_length=20)),
                ('vehicle_color', models.CharField(blank=True, help_text='د وسایطو رنګ (Vehicle Color)', max_length=50)),
                ('driving_license', models.CharField(blank=True, help_text='د موټر چلولو جواز (Driving License)', max_length=50)),
                ('tazkira_front_image', models.ImageField(blank=True, help_text='د تذکرې مخ (Tazkira Front)', upload_to='agent_docs/tazkira/')),
                ('tazkira_back_image', models.ImageField(blank=True, help_text='د تذکرې شا (Tazkira Back)', upload_to='agent_docs/tazkira/')),
                ('driving_license_image', models.ImageField(blank=True, help_text='د موټر چلولو جواز (Driving License)', upload_to='agent_docs/license/')),
                ('vehicle_registration_image', models.ImageField(blank=True, help_text='د وسایطو ثبت (Vehicle Registration)', upload_to='agent_docs/vehicle/')),
                ('profile_photo', models.ImageField(blank=True, help_text='د پروفایل انځور (Profile Photo)', upload_to='agent_photos/')),
                ('reference1_name', models.CharField(blank=True, help_text='د لومړي سپارښتونکي نوم', max_length=200)),
                ('reference1_phone', models.CharField(blank=True, help_text='د لومړي سپارښتونکي تلیفون', max_length=20)),
                ('reference1_relation', models.CharField(blank=True, help_text='اړیکه', max_length=100)),
                ('reference2_name', models.CharField(blank=True, help_text='د دویم سپارښتونکي نوم', max_length=200)),
                ('reference2_phone', models.CharField(blank=True, help_text='د دویم سپارښتونکي تلیفون', max_length=20)),
                ('reference2_relation', models.CharField(blank=True, help_text='اړیکه', max_length=100)),
                ('emergency_contact', models.CharField(blank=True, help_text='د بیړني اړیکې تلیفون', max_length=20)),
                ('emergency_relation', models.CharField(blank=True, help_text='د بیړني اړیکې اړیکه', max_length=100)),
                ('bank_name', models.CharField(blank=True, help_text='د بانک نوم', max_length=100)),
                ('account_number', models.CharField(blank=True, help_text='د حساب شمیره', max_length=50)),
                ('account_holder_name', models.CharField(blank=True, help_text='د حساب خاوند', max_length=200)),
                ('mobile_wallet', models.CharField(blank=True, help_text='د موبایل والټ شمیره', max_length=20)),
                ('status', models.CharField(choices=[('pending', 'انتظار کې (Pending)'), ('approved', 'تصویب شوی (Approved)'), ('active', 'فعال (Active)'), ('inactive', 'غیر فعال (Inactive)'), ('suspended', 'تعلیق شوی (Suspended)'), ('rejected', 'رد شوی (Rejected)')], default='pending', max_length=20)),
                ('availability', models.CharField(choices=[('available', 'شته (Available)'), ('busy', 'بوخت (Busy)'), ('offline', 'آفلاین (Offline)'), ('break', 'وقفه (Break)')], default='offline', max_length=20)),
                ('is_verified', models.BooleanField(default=False, help_text='تصدیق شوی (Verified by Admin)')),
                ('application_date', models.DateTimeField(auto_now_add=True, help_text='د غوښتنې نیټه')),
                ('approval_date', models.DateTimeField(blank=True, help_text='د تصویب نیټه', null=True)),
                ('rejection_date', models.DateTimeField(blank=True, help_text='د رد نیټه', null=True)),
                ('admin_notes', models.TextField(blank=True, help_text='د اډمین یادښتونه')),
                ('document_verification_status', models.CharField(default='pending', help_text='د اسنادو تصدیق', max_length=20)),
                ('background_check_status', models.CharField(default='pending', help_text='د شالید پلټنه', max_length=20)),
                ('training_status', models.CharField(default='pending', help_text='د روزنې حالت', max_length=20)),
                ('total_deliveries', models.PositiveIntegerField(default=0)),
                ('successful_deliveries', models.PositiveIntegerField(default=0)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(5)])),
                ('current_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('current_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('current_address', models.CharField(blank=True, max_length=500)),
                ('last_location_update', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_active', models.DateTimeField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_agents', to=settings.AUTH_USER_MODEL)),
                ('rejected_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rejected_agents', to=settings.AUTH_USER_MODEL)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_agent_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Delivery Agent Profile',
                'verbose_name_plural': 'Delivery Agent Profiles',
                'db_table': 'delivery_agent_profiles',
                'ordering': ['-application_date'],
            },
        ),
    ]
