import { useEffect, lazy, Suspense } from "react";
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
import { useAuth } from "./context/AuthContext";
import { ReviewsProvider } from "./context/ReviewsContext";
import { ConfigProvider } from "./context/ConfigContext";
import { RealTimeProvider } from "./context/RealTimeContext";
import { AdminProvider } from "./context/AdminContext";
import { Toaster } from "react-hot-toast";

// Layouts
import MainLayout from "./layouts/MainLayout";
import DashboardLayout from "./layouts/DashboardLayout";
import AuthLayout from "./layouts/AuthLayout";

// General Components
import Loader from "./components/common/Loader";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import ErrorBoundary from "./components/error/ErrorBoundary";
import RestaurantStatusChecker from "./components/restaurant/RestaurantStatusChecker";

// Pages - Customer
const Home = lazy(() => import("./pages/customer/Home.jsx"));
const RestaurantList = lazy(() =>
  import("./pages/customer/RestaurantList.jsx")
);
const RestaurantDetail = lazy(() =>
  import("./pages/customer/RestaurantDetail.jsx")
);
const Cart = lazy(() => import("./pages/customer/Cart.jsx"));
const Checkout = lazy(() => import("./pages/customer/Checkout.jsx"));
const OrderConfirmation = lazy(() =>
  import("./pages/customer/OrderConfirmation.jsx")
);
const OrderTracking = lazy(() =>
  import("./pages/customer/SimpleOrderTracking.jsx")
);
const Orders = lazy(() => import("./pages/customer/Orders.jsx"));
const Profile = lazy(() => import("./pages/customer/Profile.jsx"));
const LoyaltyDemo = lazy(() => import("./pages/customer/LoyaltyDemo.jsx"));
const Favorites = lazy(() => import("./pages/customer/Favorites.jsx"));
const SearchResults = lazy(() => import("./pages/customer/SearchResults.jsx"));
const ScheduledOrders = lazy(() =>
  import("./pages/customer/ScheduledOrders.jsx")
);
const HelpCenter = lazy(() => import("./pages/customer/HelpCenter.jsx"));
const SupportTickets = lazy(() =>
  import("./pages/customer/SupportTickets.jsx")
);
const SocialHub = lazy(() => import("./pages/customer/SocialHub.jsx"));
const MyAddresses = lazy(() => import("./pages/customer/MyAddresses.jsx"));
const DeliveryZones = lazy(() => import("./pages/customer/DeliveryZones.jsx"));

// Pages - Auth
const Login = lazy(() => import("./pages/auth/Login.jsx"));
const Register = lazy(() => import("./pages/auth/Register.jsx"));
const ForgotPassword = lazy(() => import("./pages/auth/ForgotPassword.jsx"));
const VerifyEmail = lazy(() => import("./pages/auth/VerifyEmail.jsx"));

// Pages - Restaurant Owner
const RestaurantDashboard = lazy(() =>
  import("./pages/restaurant/DashboardFixed.jsx")
);
const MyRestaurants = lazy(() =>
  import("./pages/restaurant/MyRestaurants.jsx")
);
const MenuManagement = lazy(() =>
  import("./pages/restaurant/MenuManagement.jsx")
);
const RestaurantMenuManager = lazy(() =>
  import("./pages/restaurant/RestaurantMenuManager.jsx")
);
const RestaurantOrderManager = lazy(() =>
  import("./pages/restaurant/RestaurantOrderManager.jsx")
);
const MenuCategories = lazy(() =>
  import("./pages/restaurant/MenuCategories.jsx")
);
const RestaurantAnalytics = lazy(() =>
  import("./pages/restaurant/Analytics.jsx")
);
const RestaurantProfile = lazy(() =>
  import("./pages/restaurant/RestaurantProfile.jsx")
);
const RestaurantOrders = lazy(() => import("./pages/restaurant/Orders.jsx"));
const RestaurantOrderManagement = lazy(() =>
  import("./pages/restaurant/OrderManagement.jsx")
);
const RatingDashboard = lazy(() =>
  import("./pages/restaurant/RatingDashboard.jsx")
);
const FinancialDashboard = lazy(() =>
  import("./pages/restaurant/FinancialDashboard.jsx")
);
const RegisterRestaurant = lazy(() =>
  import("./pages/restaurant/RegisterRestaurant.jsx")
);
const RegistrationSuccess = lazy(() =>
  import("./pages/restaurant/RegistrationSuccess.jsx")
);
const RestaurantPartner = lazy(() =>
  import("./pages/restaurant/RestaurantPartner.jsx")
);
const RestaurantOnboarding = lazy(() =>
  import("./pages/restaurant/RestaurantOnboarding.jsx")
);

// Pages - Delivery Agent
const DeliveryDashboard = lazy(() =>
  import("./pages/delivery/DeliveryAgentDashboard.jsx")
);
const TestDashboard = lazy(() => import("./pages/delivery/TestDashboard.jsx"));
const DeliveryOrders = lazy(() => import("./pages/delivery/OrdersPage.jsx"));
const DeliveryProfile = lazy(() => import("./pages/delivery/ProfilePage.jsx"));
const DeliveryEarnings = lazy(() =>
  import("./pages/delivery/EarningsPage.jsx")
);
const DeliveryCashCollection = lazy(() =>
  import("./pages/delivery/CashCollectionDashboard.jsx")
);
const DeliveryTestLogin = lazy(() => import("./pages/delivery/TestLogin.jsx"));
const DeliveryTestRoute = lazy(() => import("./pages/delivery/TestRoute.jsx"));
// Test Components
const EmployeeLoginTest = lazy(() => import("./pages/test/LoginTest.jsx"));
const ApiTest = lazy(() => import("./components/test/ApiTest.jsx"));
const RestaurantProfileTest = lazy(() =>
  import("./components/test/RestaurantProfileTest.jsx")
);
const CuisineUpdateTest = lazy(() =>
  import("./components/test/CuisineUpdateTest.jsx")
);

// DISABLED: Self-registration components (Employee-based system)
const RegistrationDisabled = lazy(() =>
  import("./pages/delivery/RegistrationDisabled.jsx")
);

// Additional test components
const UserRestaurantsTest = lazy(() =>
  import("./components/test/UserRestaurantsTest.jsx")
);

const EnhancedRegistration = lazy(() =>
  import("./pages/delivery/EnhancedRegistration.jsx")
);
const DeliveryRegistrationSuccess = lazy(() =>
  import("./pages/delivery/RegistrationSuccess.jsx")
);
const SmartDashboard = lazy(() =>
  import("./pages/delivery/SmartDashboard.jsx")
);

// Pages - Admin
const AdminDashboard = lazy(() => import("./pages/admin/Dashboard.jsx"));
const UserManagement = lazy(() => import("./pages/admin/UserManagement.jsx"));
const LoginTest = lazy(() => import("./pages/admin/LoginTest.jsx"));
const RestaurantManagement = lazy(() =>
  import("./pages/admin/RestaurantManagement.jsx")
);
const RestaurantApproval = lazy(() =>
  import("./pages/admin/RestaurantApprovalReal.jsx")
);
const OrderManagement = lazy(() => import("./pages/admin/ManageOrders.jsx"));
// Employee Management (New System)
const EmployeeManagement = lazy(() =>
  import("./pages/admin/EmployeeManagement.jsx")
);
const ManualOrderAssignment = lazy(() =>
  import("./pages/admin/ManualOrderAssignment.jsx")
);

// Legacy Delivery Agent Management (To be removed)
const DeliveryAgentManagement = lazy(() =>
  import("./pages/admin/DeliveryAgentManagement.jsx")
);
const DeliveryAgentApprovals = lazy(() =>
  import("./pages/admin/DeliveryAgentApprovals.jsx")
);
const DeliveryAssignments = lazy(() =>
  import("./pages/admin/DeliveryAssignments.jsx")
);
const AdminAnalytics = lazy(() => import("./pages/admin/Analytics.jsx"));
const RealTimeDemo = lazy(() => import("./pages/admin/RealTimeDemo.jsx"));

// Test Components
const RestaurantTest = lazy(() => import("./pages/admin/RestaurantTest.jsx"));
const AuthApiTest = lazy(() => import("./pages/test/AuthApiTest.jsx"));
const MenuApiTest = lazy(() => import("./pages/test/MenuApiTest.jsx"));
const MenuItemApiTest = lazy(() => import("./pages/test/MenuItemApiTest.jsx"));
const OrderApiTest = lazy(() => import("./pages/test/OrderApiTest.jsx"));
const LoginDebugTest = lazy(() => import("./pages/test/LoginDebugTest.jsx"));
const OtpTroubleshoot = lazy(() => import("./pages/test/OtpTroubleshoot.jsx"));
const AuthTest = lazy(() => import("./pages/test/AuthTest.jsx"));
const BackendTest = lazy(() => import("./pages/test/BackendTest.jsx"));
const ApiDiagnostic = lazy(() => import("./pages/test/ApiDiagnostic.jsx"));
const BackendApiTest = lazy(() => import("./pages/test/BackendApiTest.jsx"));
const EmailAuthTest = lazy(() => import("./pages/test/EmailAuthTest.jsx"));
const FavoriteTest = lazy(() => import("./pages/test/FavoriteTest.jsx"));
const RestaurantApiTest = lazy(() =>
  import("./components/restaurant/RestaurantApiTest.jsx")
);
const ApiConnectionTest = lazy(() =>
  import("./components/debug/ApiConnectionTest.jsx")
);

// Error Pages
const NotFound = lazy(() => import("./pages/error/NotFound.jsx"));

function App() {
  const { user, loading } = useAuth();
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  if (loading) {
    return <Loader fullPage />;
  }

  return (
    <ErrorBoundary>
      <ConfigProvider>
        <ReviewsProvider>
          <RealTimeProvider>
            <Suspense fallback={<Loader fullPage />}>
              <Routes>
                {/* Auth Routes */}
                <Route element={<AuthLayout />}>
                  <Route path='/login' element={<Login />} />
                  <Route path='/register' element={<Register />} />
                  <Route path='/forgot-password' element={<ForgotPassword />} />
                  <Route path='/verify-email' element={<VerifyEmail />} />
                </Route>

                {/* Public Test Routes */}
                <Route path='/login-test' element={<LoginTest />} />
                <Route
                  path='/employee-login-test'
                  element={<EmployeeLoginTest />}
                />
                <Route path='/api-test' element={<ApiConnectionTest />} />

                {/* Customer Routes */}
                <Route element={<MainLayout />}>
                  <Route path='/' element={<Home />} />
                  <Route path='/restaurants' element={<RestaurantList />} />
                  <Route
                    path='/restaurants/:id'
                    element={<RestaurantDetail />}
                  />
                  <Route
                    path='/restaurant-partner'
                    element={<RestaurantPartner />}
                  />
                  <Route path='/search' element={<SearchResults />} />
                  <Route path='/cart' element={<Cart />} />
                  <Route path='/delivery-test' element={<TestDashboard />} />
                  <Route
                    path='/delivery-login-test'
                    element={<DeliveryTestLogin />}
                  />
                  <Route
                    path='/checkout'
                    element={
                      <ProtectedRoute role='customer'>
                        <Checkout />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/orders'
                    element={
                      <ProtectedRoute role='customer'>
                        <Orders />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/order-confirmation/:id'
                    element={
                      <ProtectedRoute role='customer'>
                        <OrderConfirmation />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/orders/:id'
                    element={
                      <ProtectedRoute role='customer'>
                        <OrderTracking />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/profile'
                    element={
                      <ProtectedRoute>
                        <Profile />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/addresses'
                    element={
                      <ProtectedRoute role='customer'>
                        <MyAddresses />
                      </ProtectedRoute>
                    }
                  />
                  <Route path='/delivery-zones' element={<DeliveryZones />} />
                  <Route
                    path='/loyalty-demo'
                    element={
                      <ProtectedRoute role='customer'>
                        <LoyaltyDemo />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/favorites'
                    element={
                      <ProtectedRoute role='customer'>
                        <Favorites />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/favorite-test'
                    element={
                      <ProtectedRoute role='customer'>
                        <FavoriteTest />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/scheduled-orders'
                    element={
                      <ProtectedRoute role='customer'>
                        <ScheduledOrders />
                      </ProtectedRoute>
                    }
                  />
                  <Route path='/support' element={<HelpCenter />} />
                  <Route
                    path='/support/tickets'
                    element={
                      <ProtectedRoute role='customer'>
                        <SupportTickets />
                      </ProtectedRoute>
                    }
                  />
                  <Route path='/support/faq' element={<HelpCenter />} />
                  <Route path='/support/contact' element={<HelpCenter />} />
                  <Route
                    path='/social'
                    element={
                      <ProtectedRoute role='customer'>
                        <SocialHub />
                      </ProtectedRoute>
                    }
                  />

                  {/* Restaurant Registration Routes */}
                  <Route
                    path='/register-restaurant'
                    element={
                      <ProtectedRoute>
                        <RegisterRestaurant />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/restaurant-registration-success'
                    element={<RegistrationSuccess />}
                  />
                </Route>

                {/* Restaurant Owner Routes */}
                <Route
                  path='/restaurant'
                  element={
                    <ProtectedRoute role='restaurant'>
                      <RestaurantStatusChecker>
                        <DashboardLayout userType='restaurant' />
                      </RestaurantStatusChecker>
                    </ProtectedRoute>
                  }
                >
                  <Route index element={<RestaurantDashboard />} />
                  <Route path='dashboard' element={<RestaurantDashboard />} />
                  <Route
                    path='dashboard/:restaurantId'
                    element={<RestaurantDashboard />}
                  />
                  <Route path='my-restaurants' element={<MyRestaurants />} />
                  <Route path='onboarding' element={<RestaurantOnboarding />} />
                  <Route path='menu' element={<MenuManagement />} />
                  <Route
                    path='menu/:restaurantId'
                    element={<MenuManagement />}
                  />
                  <Route
                    path='menu-manager'
                    element={<RestaurantMenuManager />}
                  />
                  <Route
                    path='menu-manager/:restaurantId'
                    element={<RestaurantMenuManager />}
                  />
                  <Route path='menu-categories' element={<MenuCategories />} />
                  <Route path='profile' element={<RestaurantProfile />} />
                  <Route path='orders' element={<RestaurantOrders />} />
                  <Route
                    path='orders/:restaurantId'
                    element={<RestaurantOrders />}
                  />
                  <Route
                    path='order-manager'
                    element={<RestaurantOrderManager />}
                  />
                  <Route
                    path='order-manager/:restaurantId'
                    element={<RestaurantOrderManager />}
                  />
                  <Route
                    path='order-management'
                    element={<RestaurantOrderManagement />}
                  />
                  <Route path='analytics' element={<RestaurantAnalytics />} />
                  <Route
                    path='analytics/:restaurantId'
                    element={<RestaurantAnalytics />}
                  />
                  <Route path='ratings' element={<RatingDashboard />} />
                  <Route path='financial' element={<FinancialDashboard />} />
                </Route>

                {/* Delivery Agent Routes */}
                <Route
                  path='/delivery'
                  element={
                    <ProtectedRoute role='delivery_agent'>
                      <DashboardLayout userType='delivery' />
                    </ProtectedRoute>
                  }
                >
                  <Route index element={<DeliveryDashboard />} />
                  <Route path='dashboard' element={<DeliveryDashboard />} />
                  <Route path='test' element={<TestDashboard />} />
                  <Route path='orders' element={<DeliveryOrders />} />
                  <Route path='orders/:id' element={<DeliveryOrders />} />
                  <Route path='profile' element={<DeliveryProfile />} />
                  <Route path='earnings' element={<DeliveryEarnings />} />
                  <Route
                    path='cash-collection'
                    element={<DeliveryCashCollection />}
                  />
                  <Route path='smart-dashboard' element={<SmartDashboard />} />
                </Route>

                {/* Delivery Agent Registration - Redirected to Disabled Page */}
                <Route
                  path='/delivery/register'
                  element={<RegistrationDisabled />}
                />
                <Route
                  path='/delivery/registration-success'
                  element={<RegistrationDisabled />}
                />
                <Route
                  path='/delivery/registration-disabled'
                  element={<RegistrationDisabled />}
                />

                {/* Admin Routes */}
                <Route
                  path='/admin'
                  element={
                    <ProtectedRoute role='admin'>
                      <AdminProvider>
                        <DashboardLayout userType='admin' />
                      </AdminProvider>
                    </ProtectedRoute>
                  }
                >
                  <Route
                    index
                    element={<Navigate to='/admin/dashboard' replace />}
                  />
                  <Route path='dashboard' element={<AdminDashboard />} />
                  <Route path='users' element={<UserManagement />} />
                  <Route
                    path='restaurants'
                    element={<RestaurantManagement />}
                  />
                  <Route
                    path='restaurant-approvals'
                    element={<RestaurantApproval />}
                  />
                  <Route path='orders' element={<OrderManagement />} />

                  {/* Employee Management (New System) */}
                  <Route path='employees' element={<EmployeeManagement />} />
                  <Route
                    path='order-assignments'
                    element={<ManualOrderAssignment />}
                  />

                  {/* Legacy Delivery Agent Routes (To be removed) */}
                  <Route
                    path='delivery-agents'
                    element={<DeliveryAgentManagement />}
                  />
                  <Route
                    path='delivery-agent-approvals'
                    element={<DeliveryAgentApprovals />}
                  />
                  <Route
                    path='delivery-assignments'
                    element={<DeliveryAssignments />}
                  />
                  <Route path='analytics' element={<AdminAnalytics />} />
                  <Route path='realtime-demo' element={<RealTimeDemo />} />

                  <Route path='restaurant-test' element={<RestaurantTest />} />
                  <Route
                    path='restaurant-api-test'
                    element={<RestaurantApiTest />}
                  />
                  <Route path='auth-api-test' element={<AuthApiTest />} />
                  <Route path='menu-api-test' element={<MenuApiTest />} />
                  <Route
                    path='menu-item-api-test'
                    element={<MenuItemApiTest />}
                  />
                  <Route path='order-api-test' element={<OrderApiTest />} />
                  <Route path='login-debug-test' element={<LoginDebugTest />} />
                  <Route
                    path='otp-troubleshoot'
                    element={<OtpTroubleshoot />}
                  />
                  <Route path='auth-test' element={<AuthTest />} />
                  <Route path='backend-test' element={<BackendTest />} />
                  <Route path='api-diagnostic' element={<ApiDiagnostic />} />
                  <Route path='backend-api-test' element={<BackendApiTest />} />
                  <Route path='email-auth-test' element={<EmailAuthTest />} />
                  <Route path='favorite-test' element={<FavoriteTest />} />
                  <Route path='login-test' element={<LoginTest />} />
                  <Route path='api-test' element={<ApiTest />} />
                  <Route
                    path='restaurant-profile-test'
                    element={<RestaurantProfileTest />}
                  />
                  <Route
                    path='cuisine-update-test'
                    element={<CuisineUpdateTest />}
                  />
                  <Route
                    path='user-restaurants-test'
                    element={<UserRestaurantsTest />}
                  />
                  <Route
                    path='api-connection-test'
                    element={<ApiConnectionTest />}
                  />
                  <Route
                    path='test'
                    element={
                      <div style={{ padding: "20px" }}>
                        🧪 Test Route Working! Admin routes are functional.
                      </div>
                    }
                  />
                </Route>

                {/* 404 Route */}
                <Route path='*' element={<NotFound />} />
              </Routes>
            </Suspense>
          </RealTimeProvider>
        </ReviewsProvider>
      </ConfigProvider>

      {/* Toast Notifications */}
      <Toaster
        position='top-right'
        reverseOrder={false}
        gutter={8}
        containerClassName=''
        containerStyle={{}}
        toastOptions={{
          // Define default options
          className: "",
          duration: 4000,
          style: {
            background: "#363636",
            color: "#fff",
            borderRadius: "8px",
            padding: "16px",
            fontSize: "14px",
            fontWeight: "500",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
          },
          // Default options for specific types
          success: {
            duration: 3000,
            style: {
              background: "#10B981",
              color: "#fff",
            },
            iconTheme: {
              primary: "#fff",
              secondary: "#10B981",
            },
          },
          error: {
            duration: 5000,
            style: {
              background: "#EF4444",
              color: "#fff",
            },
            iconTheme: {
              primary: "#fff",
              secondary: "#EF4444",
            },
          },
          loading: {
            style: {
              background: "#3B82F6",
              color: "#fff",
            },
          },
        }}
      />
    </ErrorBoundary>
  );
}

export default App;
