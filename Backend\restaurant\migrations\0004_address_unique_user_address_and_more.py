# Generated by Django 5.0.4 on 2025-04-26 06:54

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('restaurant', '0003_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddConstraint(
            model_name='address',
            constraint=models.UniqueConstraint(fields=('user', 'street', 'city', 'postal_code'), name='unique_user_address'),
        ),
        migrations.AddConstraint(
            model_name='menucategory',
            constraint=models.UniqueConstraint(fields=('restaurant', 'name'), name='unique_category_per_restaurant'),
        ),
        migrations.AddConstraint(
            model_name='menuitem',
            constraint=models.UniqueConstraint(fields=('category', 'name'), name='unique_item_per_category'),
        ),
        migrations.AddConstraint(
            model_name='restaurant',
            constraint=models.UniqueConstraint(condition=models.Q(('is_active', True)), fields=('owner', 'name'), name='unique_restaurant_per_owner'),
        ),
    ]
